{"i18nData": {"title": "GCUBE AI PLATFORM", "menu_pricing": "GPUs Pricing", "menu_dashboard": "DASHBOARD", "menu_nodes": "NODES", "menu_workloads": "WORKLOADS", "menu_profile": "My Profile", "menu_point": "Point", "menu_point_charge": "Charge Point", "menu_point_settlement": "Settlement Point", "menu_logout": "Logout", "menu_docs": "Docs", "menu_personal_setting": "Setting", "menu_personal_payment": "My Payments", "menu_personal_storage": "Personal Storage", "but_more": "more", "but_more_gpu": "More GPUs", "but_logout": "Logout", "but_ok": "OK", "but_cancel": "Cancel", "but_close": "Close", "but_edit": "Edit", "but_search": "Search", "but_selected_delete": "Delete selections", "but_delete": "Delete", "but_modify": "Modify", "but_deploy": "Deploy", "but_monitoring": "Monitoring", "but_start": "Start", "but_stop": "Stop", "but_register": "Register", "but_receipt": "Receipt", "but_history": "History", "but_payment": "Payment", "but_payment_retry": "Retry Payment", "but_payment_approval": "Payment Approval", "but_payment_finish": "Payment Finish", "but_settlement_withdrawal_request": "Withdrawal Request", "but_register_node": "Connect New Node", "but_register_workload": "Register New Workload", "but_set_price": "Set Price", "but_income_history": "Income History", "but_spend_history": "Spend History", "but_cloud_edit": "Cloud Edit", "but_history_details": "Details", "but_container_logs": "Container Logs", "but_container_terminal": "Container Terminal", "but_container_ssh": "Container SSH", "but_container_event": "Pod Events", "but_container_deploy_event": "Deploy Events", "but_register_credential": "Register new Credential", "but_point_charge": "Point Charge", "but_point_settlement": "Point Settlement", "but_point_all": "<PERSON>", "but_signup": "SIGN UP", "but_signin": "Sign In", "but_retry_login": "Retry login", "but_next": "Next", "but_profile_edit": "Edit Profile", "but_contactus": "Contact Us", "but_register_pod_ssh": "Register SSH Info", "but_delete_pod_ssh": "Delete SSH Info", "but_search_pod_ssh_publicIP": "Search Pubilc IP", "but_session_extends_setting": "Configure Session", "but_other_repo": "Choose a different repository", "but_register_credential_info": "Register credentials", "but_settlement_request": "Settlement Request", "but_point_refund_request": "Point Refund", "but_point_used": "Usage", "but_node_taint_event_list": "Node blocking history", "but_dedicated_workload_status": "Dedicated Node Status", "but_worklaod_operational_status": "Operational Status", "but_stop_watching_today": "Stop watching today", "dialog_title_ok": "OK", "dialog_title_waring": "Waring", "dialog_title_error": "Error", "dialog_title_notice": "Notice", "msg_confirm_logout": "Do you want to logout?", "msg_desc_logout": "If you are ready to end your current session, select \"LogOut\" below.", "msg_no_content": "No Content Found", "resource_gpu": "GPU", "resource_gpu_memory": " GPU Memory", "resource_cpu": "CPU", "resource_memory": " RAM", "resource_disk": "DISK", "resource_network": "Network", "resource_gpu_usage": "GPU Usage", "resource_gpu_memory_usage": "VRAM Usage", "resource_cpu_usage": "CPU Usage", "resource_memory_usage": "Memory Usage", "resource_disk_usage": "Disk Usage", "resource_disk_occu": "Disk Occupation", "resource_network_traffic": "Network Traffic", "resource_network_traffic_in": "Network Traffic Rx", "resource_network_traffic_out": "Network Traffic Tx", "resource_network_speed": "Network Speed", "cloud_ncp": "Naver Cloud", "cloud_aws": "Amazon Web Services", "cloud_gcp": "Google Cloud", "cloud_azure": "Microsoft Azure", "cloud_lambda": "Lambda Cloud", "repo_awsecr": "Amazon ECR", "repo_dockerhub": "<PERSON><PERSON>", "repo_nvidia": "NVIDIA", "repo_github": "<PERSON><PERSON>", "repo_redhat": "Red Hat Quay", "repo_huggingface": "Hugging Face", "form_label_select_time": "Select Time Range", "form_label_customize_time": "Customize Time Range", "form_label_start_time": "Start time", "form_label_end_time": "End time", "form_label_interval": "Sampling Interval", "form_select_options_interval_1min": "1 minutes", "form_select_options_interval_2min": "2 minutes", "form_select_options_interval_3min": "3 minutes", "form_select_options_interval_5min": "5 minutes", "form_select_options_interval_10min": "10 minutes", "form_select_options_interval_15min": "15 minutes", "form_select_options_interval_1hour": "1 Hour", "form_select_options_interval_2hour": "2 Hour", "form_select_options_interval_3hour": "3 Hour", "form_select_options_interval_5hour": "5 Hour", "form_select_options_time_30min": "Last 30 minutes", "form_select_options_time_1hour": "Last 1 hour", "form_select_options_time_2hour": "Last 2 hour", "form_select_options_time_3hour": "Last 3 hour", "form_select_options_time_5hour": "Last 5 hour", "form_select_options_time_8hour": "Last 8 hour", "form_select_options_time_12hour": "Last 12 hour", "form_select_options_time_1day": "Last 1 days", "form_select_options_time_2day": "Last 2 days", "form_select_options_time_3day": "serLast 3 days", "form_select_options_time_7day": "Last 7 days", "table_th_date": "Date", "table_th_hour": "Hour", "table_th_node": "Node", "table_th_workload": "Workload", "table_th_pod": "Pod", "table_th_point": "Point", "table_th_point_income": "Income Points", "table_th_point_spend": "Spend Points", "table_th_amount": "Amount", "table_th_amount_unit": "Unit Amount", "table_th_amount_usage": "Usage Amount", "table_th_gpu": "GPU", "table_th_gpu_name": "GPU Name", "table_th_gpu_count": "GPUs", "table_th_gpu_memory": "Total GPU Memory", "table_th_cpu_count": "vCPUs", "table_th_memory": "Memory", "table_th_disk": "Disk", "table_th_price": "Price per hour", "table_th_price_unit": "Unit Price per hour", "table_th_price_usage": "Usage Price per hour", "table_th_update_time": "Update Time", "table_th_use_time": "Use Time", "table_th_gpu_usage": "GPU Usage", "table_th_gpu_memory_usage": "GPU Memory Usage", "table_th_cpu_usage": "CPU Usage", "table_th_memory_usage": "Memory Usage", "table_th_disk_usage": "Disk Usage", "table_th_network_usage": "Newtork Usage", "table_th_payment_time": "Payment time", "table_th_payment_method": "Payment method", "table_th_payment_currency": "<PERSON><PERSON><PERSON><PERSON>", "table_th_payment_amount": "Amount", "table_th_payment_charge_point": "Charge Point", "table_th_payment_status": "Status", "table_th_payment_receipt": "Receipt", "table_th_product_name": "Name", "table_th_gpu_model": "GPU Model", "table_th_pod_event_res_type": "Response Type", "table_th_pod_event_type": "Event Type", "table_th_pod_event_reason": "Event Reason", "table_th_pod_event_component": "Reporting Component", "table_th_pod_event_kind": "Involved Object Kind", "table_th_pod_event_manager": "Manager", "table_th_pod_event_operation": "Operation", "table_th_pod_event_count": "Count", "table_th_pod_event_create_at": "Created At", "table_th_pod_event_last_at": "Last Seen", "table_th_pod_event_detailed": "Detailed", "table_th_description": "Description", "table_th_balance_after_settlement": "Balance after settlement", "table_th_requested_point": "Requested points", "table_th_settlement_point": "Settlement points", "table_th_tax_deduction": "Tax deduction (3.3%)", "table_th_exchange_rate": "Exchange rate", "table_th_with_or_without_settlement": "With or without settlement", "table_th_request_date": "Request date", "table_th_settlement_date": "Settlement date", "table_th_transfer_points": "Transfers Points", "table_th_charge_type": "Charge Type", "table_th_charge_points": "Charge Points", "table_th_charge_balance": "Balance", "table_th_charge_date": "Charge Time", "table_th_charge_used": "Is Used", "table_th_refund_balance": "Balance after refund", "table_th_refund_points": "Refund Points", "table_th_refund_transfer_points": "Transfer points", "table_th_refund_fee": "Fee", "table_th_refund_status": "Refund Status", "table_th_refund_request_date": "Request Date", "table_th_refund_time": "Refund Time", "table_th_node_name": "Name", "table_th_node_gpu_model": "GPU Model", "table_th_node_gpu_memory": "GPU Memory", "table_th_node_gpu_driver": "Graphics Driver", "table_th_node_gpu_cuda": "CUDA Toolkit", "table_th_node_hostspec": "Host Spec", "table_th_node_state": "Running state", "table_th_node_connected_status": "Connected status", "table_th_node_state_history": "State history", "table_th_node_taint": "Status", "table_th_node_taint_type": "type", "table_th_node_taint_content": "Error occurred", "table_th_node_taint_turationAt": "Error duration", "table_th_node_taint_cancelAt": "Occurred time", "table_th_node_taint_settingAt": "Cleared time", "table_td_node_state_detail": "Detail", "table_td_sum": "Sum", "select_cuda_version": "Select a version", "node_no_content": "You do not have any Nodes yet", "node_status_open": "Open", "node_status_provision": "Provision", "node_status_running": "Running", "node_status_fail": "Fail", "node_status_stop": "Stop", "node_status_created": "Created", "node_label_category": "Node Category", "node_label_gpuspec": "GPU Spec", "node_label_hostspec": "Host Spec", "node_label_driver": "Graphics Driver", "node_label_cuda": "CUDA Toolkit", "node_label_createdat": "Created At", "node_label_lastseen": "Last Seen", "node_label_income": "Income", "node_label_unitprice": "Unit Price", "node_label_usageprice": "Usage Price", "node_label_name": "Name", "node_label_address": "Address", "node_label_state": "State", "node_label_substate": "Sub State", "node_label_shared": "Shared", "node_label_vpnpubkey": "VPN Public Key", "node_label_cloud_instid": "Instance ID", "node_label_cloud_region": "Region", "node_label_cloud_vmip": "VM IP Address", "node_label_cloud_unitprice": "Unit Price", "node_register_title": "Node Register", "node_register_step1_title": "Download GCUBE Agent Program", "node_register_step1_msg": "Download GCUBE Agent to register new devices.", "node_register_step2_title": "Installation specifications", "node_register_step2_msg": "Check out our installation recommendations.", "node_register_step2_minspec": "Minimum specifications", "node_register_step2_recspec": "Recommended specifications", "node_register_step2_basicspec": "Basic specifications", "node_register_step3_title": "Running Agent", "node_register_step3_msg": "Authenticate and launch your new device.", "node_info_title": "Node Information", "node_info_node": "Node", "node_info_devicespec": "Device Spec", "node_info_cloud": "Cloud", "node_monitoring_title": "Node Monitoring", "node_delete_title": "Delete Node", "node_delete_msg_confirm": "Do you want to delete this node ?", "node_delete_pc_msg_confirm": "Use the Agent to delete it <br/> If the deletion fails, use the platform's delete feature.", "node_delete_msg_failed": "Node delete failed.", "node_state_change_title": "Node Change State", "node_state_change_msg_confirm": "Do you want to {0} this node ?", "node_state_change_msg_failed": " 'Node {0} failed_'", "node_cloud_edit_title": "Naver Cloud Parameters", "node_cloud_ncp_edit_title": "Naver Cloud 파라미터", "node_cloud_nhn_edit_title": "NHN Cloud 파라미터", "node_cloud_edit_failed": "Edit Failed", "node_cloud_edit_invalid_json": "Invalid json string", "node_setprice_title": "Node Set Price", "node_setprice_history": "Set Price History", "node_setprice_price_unit": "Unit Price", "node_setprice_price_usage": "Usage Price", "node_setprice_price_hour": "Per Hour", "node_setprice_price_unit_hour": "Unit Price per hour", "node_setprice_price_usage_hour": "Usage Price per hour", "node_setprice_msg_price_required": "Usage Price is required", "node_setprice_msg_price_range": "Pricing is from {0} to {1}", "node_setprice_msg_price_same_value": "It's same values", "node_setprice_msg_failed": "Fail update price", "node_setprice_msg_running_pod": "You can't set a price because there are running Pods.", "node_setprice_msg_confirm": "Would you like to set the price as follows?", "node_setprice_msg_no_content": "There is no set price information.", "node_setprice_desc01": "The adjustment range for the basic hourly rate and the hourly usage rate can only be set up to 75% of the selected basic unit price. <br>The exchange rate is applied according to the month of use.", "node_setprice_desc02": "※ The fee may change over time.", "node_setprice_desc03": "※ The set price may be adjusted by a set percentage when the unit cost of GPU utilization changes.", "node_setprice_unitprice_desc01": "Current set price : {0}% of usage amount", "node_setprice_unitprice_desc02": "Setable price : ₩ 0 ~ ₩ {0}", "node_setprice_usageprice_desc01": "Current set price : {0}% of usage amount", "node_setprice_usageprice_desc02": "Setable price : ₩ 0 ~ ₩ {0}", "node_income_history_title": "Income History", "node_income_history_details_title": "Income Detail History", "node_state_taint_grpc_msg": "There is an issue with network connectivity.<br /> Please check the network.", "node_state_taint_gpuspec_msg": "Agent failed to recognize the GPU.<br /> Please install the latest graphics driver,<br /> and perform the graphics driver reinstallation process in gcube Agent. <br />(If this does not work, please contact us).", "node_state_taint_vast_msg": "V.rented", "node_state_taint_speed_msg": "Slow network speed<br />Check your network.", "node_state_taint_syml_msg": "Container storage error<br />Please request technical support.", "node_state_taint_diskfull_msg": "Insufficient disk space<br />Increase your disk capacity.", "node_no_register_gpu": "A GPU model that is not registered with a GCUBE model.", "workload_no_content": "You do not have any Workloads yet", "workload_status_deploy": "Deploy", "workload_status_finish": "Finish", "workload_status_open": "Open", "workload_category_inference": "Inference", "workload_category_learning": "Learning", "workload_category_batch": "<PERSON><PERSON>", "workload_label_overview": "Overview", "workload_label_workload_number": "Workload #", "workload_label_state": "State", "workload_label_description": "Description", "workload_label_desc": "Workload Description", "workload_label_desc_placeholder": "Please enter a description.", "workload_label_target_node": "Target Node", "workload_label_target_spec": "Target Spec", "workload_label_target_gpu": "GPU", "workload_label_target_gpu2": "Target GPU", "workload_label_target_gpu_memory": "GPU Memory", "workload_label_target_cuda": "Minimum CUDA version", "workload_label_replica": "Replica", "workload_label_price_max_hour": "Max Price per hour", "workload_label_createdat": "Created At", "workload_label_deployat": "Deploy At", "workload_label_closeat": "Close At", "workload_label_cluster": "Cluster", "workload_label_service_port": "Cluster Service Port", "workload_label_service_url": "Service URL", "workload_label_category": "Category", "workload_label_container": "Container", "workload_label_container_image": "Container Image", "workload_label_container_command": "Container Command", "workload_label_container_port": "Container Port", "workload_label_repo_auth_check": "Repository Auth Check", "workload_label_repo_auth_msg": "Used for personal storage authentication (credentials can be registered in your profile).", "workload_label_container_envs": "Container Environment Variable", "workload_label_container_shared_memory": "Shared Memory", "workload_label_direct_deploy": "Direct Deploy", "workload_label_storage": "Persistent Volume", "workload_label_storage_capacity": "Capacity", "workload_label_storage_mount_path": "Mount Path", "workload_label_storage_class": "Class", "workload_label_repo_type": "Repository Type", "workload_label_price_total": "Total Expenses", "workload_label_price_unit": "Unit Price", "workload_label_price_usage": "Usage Price ", "workload_label_amount_total": "Total Estimated Amount", "workload_label_vat": "VAT excluded", "workload_label_options": "Option", "workload_label_cuda": "CUDA", "workload_label_gpu_status": "GPU Status", "workload_label_avilable_gpu": "View only available GPUs", "workload_label_image_verification": "Image Verification", "workload_label_verification_complete": "Verification Complete", "workload_label_personal_storage": "Personal Storage", "workload_label_refundable_points": "Refundable Points", "workload_label_payment": "Payment", "workload_label_free": "Free", "workload_label_workload": "Workload", "workload_label_network": "Network", "workload_label_refund": "Refund", "workload_label_request": "Request", "workload_label_charge_payment": "Payment Charge", "workload_label_charge_free": "Fee Charge", "workload_label_charge_cancel": "Cancel Charge", "workload_label_charge_point_used_setting": "Set up point usage", "workload_msg_charge_point_used_setting": "Would you like to enable point {0}?", "workload_msg_charge_point_used_failed": "Failed to set up point usage", "workload_label_points_charge_history": "Points Charge History", "workload_label_points_refund_history": "Points Refund History", "workload_info_title": "Workload Information", "workload_info_deployment_status": "Deployment Status", "workload_info_table_th_node": "Node", "workload_info_table_th_pod": "Pod", "workload_info_table_th_status": "Status", "workload_container_logs_title": "Container Logs", "workload_container_terminal_title": "Container Terminal", "workload_container_terminal_exit": "The Container Terminal has exited.", "workload_register_title": "Workload Register", "workload_register_msg_description_required": "Description is required", "workload_register_msg_repo_required": "Repository Type is required.", "workload_register_msg_container_image_required": "Container Image is required", "workload_register_msg_container_image_check_required": "Validate the container image URL. ", "workload_register_msg_container_port_required": "Container Port is required", "workload_register_msg_gpu_required": "GPU is required", "workload_register_msg_gpu_memory_required": "GPU Memory is required", "workload_register_msg_replica_required": "Replica is required", "workload_register_msg_price_max_required": "Max Price per hour is required", "workload_register_msg_failed": "Workload registration failed.", "workload_register_msg_deploy_failed": "Workload deploy failed.", "workload_register_msg_payment_required": "All points have been used up.<br>Please recharge your points and use them.", "workload_register_msg_price_desc": "You can calculate the estimated usage fee by selecting the service and entering the usage amount. The amount calculated by the fee calculator is an estimated fee that is different from the actual settlement amount. <br>The exchange rate is applied according to the month of use.", "workload_register_msg_shared_memory_required": "Set to at least 1 GB and no more than {0} GB.", "workload_modify_title": "Workload Modify", "workload_modify_msg_failed": "Workload modify failed.", "workload_delete_title": "Delete Workload", "workload_delete_msg_failed": "Workload delete failed.", "workload_delete_msg_confirm": "Do you want to delete this workload ?", "workload_state_change_title": "Workload {0}", "workload_state_change_msg_confirm": "Do you want to <span style=\"color: #be2617\">{0}</span> this workload ?", "workload_state_change_msg_failed": " Workload <span style=\"color: #be2617\">{0}</span> failed.", "workload_monitoring_title": "Workload Monitoring", "workload_spend_history_title": "Spend history", "workload_spend_history_details_title": "Detailed spending history", "workload_form_node_any": "Any", "workload_form_node_tier1": "Tier1", "workload_form_node_tier2": "Tier2", "workload_form_node_tier3": "Tier3", "workload_form_select_gpu": "GPU SELECT", "workload_spend_total": "Total Spend", "workload_point_payment_required": "Recharge your points.", "workload_pod_events": "Pod Events", "workload_pod_deploy_events": "Deploy events", "workload_form_numberof_available_nodes": "Number of available nodes", "worklaod_operational_status": "Operational Status", "worklaod_operational_status_detail": "Operational details", "worklaod_dedicated_node_list": "Dedicated node list", "worklaod_dedicated_node_detail": "Node detail history", "workalod_tooltip_workload_registered_gpu": "GPUs currently registered to the workload", "workalod_tooltip_gpu_available": "Available", "workalod_tooltip_gpu_unavailable": "Unavailable", "workload_containerImage_message_not_found_exposed_ports": "No public 'Port' information is available for the image.", "workload_containerImage_error_message_access_token": "You do not have permission to access the image. Please check your authentication details in your profile.", "workload_containerImage_error_message_not_found_tag": "Unable to retrieve 'Tag' information for the image. Please check the repository type, image address, and authentication details in your profile.", "workload_containerImage_error_message_not_found_exposed_ports": "Unable to retrieve public 'Port' information. Please check the repository type, image address, and authentication details in your profile.", "workload_containerImage_error_message_manifests": "Unable to retrieve the Manifests information of the container image.", "workload_containerImage_error_error_message": "An unknown error has occurred on the platform. Please contact the administrator.", "workload_containerImage_error_message_not_found_exposed_ports2": "Unable to retrieve public 'Port' information.", "workload_containerImage_error_message_check_credential_address": "Check your profile for your credentials and container image address.", "workload_modify_gpu_warning_message": "There are no nodes with the GPU specification you selected,<br />you might experience issues deploying your workload.<br />Are you sure you want to change this?", "workload_noselected_gpu_warning_message": "There is no GPU selected <br />Please select a GPU.", "workload_credential_msg": "DockerHub repositories require DockerHub account authentication.<br/>Please register and use your personal credentials.<br />If you don't have a DockerHub account, please open a new account and register, or select a different repository.", "workload_credential_msg2": "When using DockerHub repositories<br />DockerHub account authentication is required. <br />Please register and use your personal credentials.<br />If you do not have a DockerHub account, please open a new account and register or select a different repository.<br />(Terminate workloads when credentials are not registered)", "workload_pv_message01": "* To use a personal storage, you can select it after registering a personal storage.", "workload_pv_message02": "* After selecting a storage, enter the container directory to save data.", "workload_pv_link": "Go to registration", "pod_label_ssh": "Cotainer SSH Connection ", "pod_label_publicIp": "Public IP", "pod_label_ssh_url": "SSH address", "pod_label_ssh_port": "SSH port", "pod_label_ssh_publicIp": "Public IP", "pod_label_ssh_user_name": "User name", "pod_label_ssh_user_pwd": "User password", "pod_ssh_description": "Container SSH access requires you to register a public IP first.", "pod_ssh_info_description": "If your public IP address changes, please delete your access information and register your public IP again.", "pod_publicIp_required": "Public IP is required", "pod_ssh_delete_description": "Are you sure you want to delete your SSH connection information?", "pod_ssh_register_msg_failed": "Failed to register SSH access information", "pod_ssh_delete_msg_failed": "Failed to delete SSH connection information", "pod_status_pending": "Pending", "pod_status_running": "Running", "pod_status_succeeded": "Succeeded", "pod_status_failed": "Failed", "pod_status_terminating": "Terminating", "pod_status_unknows": "Unknows", "pod_event_res_type_added": "ADDED", "pod_event_res_type_modified": "MODIFIED", "pod_event_res_type_deleted": "DELETED", "pod_event_type_warning": "Warning", "pod_event_type_normal": "Normal", "pod_event_kind_update": "Update", "dashboard_cluster_status": "Cluster Status", "dashboard_node_status": "Node Status", "dashboard_workload_status": "Workload Status", "dashboard_pod_status": "Pod Status", "dashboard_cluster_resource": "Cluster Resource", "user_label_email": "E-Mail", "user_label_name": "Name", "user_label_cellphone": "Cell Phone", "user_label_namespace": "Namespace", "user_label_company": "Company Name", "user_label_position": "Job and Position", "user_label_createdat": "Created At", "user_label_lastseen": "Last Seen", "user_label_point_charge": "Points Charge", "user_label_point_amount": "Amount of payment", "user_label_point_payment_req_info": "Payment request info", "user_label_point_payment_customer_name": "Buyer name", "user_label_point_payment_approval_number": "Approval number", "user_label_point_payment_method": "Payment method", "user_label_point_payment_datetime": "Payment date and time", "user_label_point_payment_type": "Payment type", "user_label_point_payment_goods": "Payment recharge", "user_label_point_payment_info_detail": "Detailed payment information", "user_label_point_payment_order_krw": "KRW", "user_label_point_payment_order_amount": "Order amount", "user_label_point_payment_order_vat": "VAT", "user_label_point_payment_order_point": "Payment recharge points", "user_label_point_payment_amount": "Payment amount", "user_label_point_payment_vat": "VAT included", "user_label_point_payment_approval_msg": "After checking the point recharge details,<br/>please click the payment completion button below.", "user_label_point_payment_error_code": "Error Code", "user_label_point_payment_error_msg": "Error Message", "user_label_point_settlement_bank_account": "Bank Account information", "user_label_point_settlement_bank_account_name": "Account Name", "user_label_point_settlement_bank_number": "Bank Account Number", "user_label_point_settlement_bank_name": "Bank Name", "user_label_point_settlement_kakao_bank": "Kakao Bank Authentication", "user_label_point_settlement_person": "Individuals", "user_label_point_settlement_business": "Businesses(individuals)", "user_label_point_settlement_info": "Settlement Information", "user_label_point_settlement_msg01": "Corporate businesses are not eligible for settlement.", "user_label_point_settlement_msg02": "I don't have a registered account<br/>Please verify with Kakao Bank below.", "user_label_point_settlement_msg03": "The settlement account can only be used with Kakao Bank.", "user_label_point_settlement_msg04": "※The final withdrawal amount is the price at the dollar exchange rate.<br />※The amount will be deposited excluding 3.3% tax deduction.<br/>※The withdrawal date is the 15th of the following month.<br/>※If the withdrawal date falls on a holiday, it will be processed on the next business day.", "user_label_point_settlement_account_verification_success": "Verification has been completed.", "user_label_point_settlement_account_verification_error": "Verification error.", "user_label_storage_person_storage_register": "Register Personal Storage", "user_label_storage_upload_file": "Upload the rclone.conf file.", "user_label_storage_list": "Storage List", "user_label_storage_type": "Storage Type", "user_label_storage_description": "Storage Description", "user_label_storage_remote_path": "Storage Path (Enter {0} of the network driver)", "user_label_storage_s3_directory": "Bucket/Directory", "user_label_storage_directory": "Directory", "user_label_storage_access_mode": "Storage Access Mode (Default ReadWriteMany)", "user_label_storage_capacity": " Storage Capacity (Enter the network storage capacity. Enter a value larger than the actual capacity. Default 10Gi)", "user_label_storage_table_th_type": "Type", "user_label_storage_table_th_desc": "Description", "user_label_storage_table_th_pv_status": "PV(Persistent Volume) Status", "user_label_storage_table_th_pvc_status": "PVC(Persistent Volume Claim) Status", "user_label_storage_table_th_token_exp_date": "Token Expiration Date", "user_label_storage_table_th_created_at": "Registration Date", "user_label_storage_table_th_last_seen": "Last Modified Date", "user_label_storage_table_th_remote_path": "Remote Path", "user_label_storage_table_th_capacity": "Capacity", "user_label_storage_table_header_message01": "* When using a workload, you can link and use a personal storage for storing data.", "user_label_storage_table_header_message02": "* After registering a personal storage, select a personal storage when creating a workload to proceed with the setup.", "user_label_storage_table_docs_link": "View the user guide.", "user_label_pv_pvc_create": "PV & PVC Creation", "user_label_pv_pvc_update": "PV & PVC Update", "user_label_but_edit_credentials": "Modify Credentials", "user_label_storage_delete": "Delete User Storage", "user_label_storage_delete_failed": "Failed to delete storage.", "user_label_storage_delete_message": "Do you want to delete this user storage?", "user_label_storage_result_message01": "Unauthorized request.", "user_label_storage_result_message02": "Unauthorized request.", "user_label_storage_result_message03": "Invalid request.", "user_label_storage_result_message04": "Secret update error.", "user_label_storage_result_message05": "PVC update error.", "user_label_storage_result_message06": "PV update error.", "user_label_storage_result_message07": "Platform error.", "user_profile_user_info": "User Information", "user_profile_edit_title": "Edit Profile", "user_profile_edit_msg_failed": "Profile edit failed", "user_profile_user_credentials": "User Credentials", "user_credential_no_content": "No Credentials.", "user_credential_register_title": "Register Credential", "user_credential_register_msg_access_key_required": "Access key is required.", "user_credential_register_msg_secret_key_required": "Secret key (Secret Key / User Password / Access Token) is required.", "user_credential_register_msg_region_required": "Region is required.", "user_credential_register_msg_registryid_required": "Registry ID is required.", "user_credential_register_msg_failed": "User credential register failed", "user_credential_modify_title": "Modify Credential", "user_credential_modify_msg_failed": "User credential modify failed", "user_credential_delete_title": "Delete Credential", "user_credential_delete_msg_failed": "User credential delete failed", "user_credential_delete_msg_confirm": "Do you want to delete this credentials ?", "user_credential_label_category": "Category", "user_credential_label_cloud": "Cloud", "user_credential_label_repository": "Repository", "user_credential_label_access_key": "Access Key", "user_credential_label_secret_key": "Secret Key", "user_credential_label_access_key_id": "Access Key ID", "user_credential_label_secret_key_id": "Secret Access Key", "user_credential_label_user_name": "User name", "user_credential_label_user_password": "User password", "user_credential_label_user_token": "User <PERSON>", "user_credential_label_user_passwordtoken": "User password & Token", "user_credential_label_user_password_pat": "Personal Access Token(PAT) & User password", "user_credential_label_region": "Region", "user_credential_label_registryid": "Regstry ID / Account ID", "user_credential_table_th_category": "Category", "user_credential_table_th_type": "Repositories", "user_credential_table_th_access_key": "Access key / User name", "user_credential_table_th_secret_key": "Secret Key / User password", "user_point": "Points", "user_point_title": "User Point", "user_point_status": "Point Status", "user_point_network": "Point Network", "user_point_available": "Available Points", "user_point_income_total": "Total Income Points", "user_point_spend_total": "Total Spend Points", "user_point_settlement_total": "Total Settlement Points", "user_point_payment_total": "Total Payment Points", "user_point_income_month": "This Month Income Points", "user_point_spend_month": "This Month Spend points", "user_point_month": "This Month Points", "user_point_history_title": "Point History", "user_point_income": "Income Points", "user_point_spend": "Spend Points", "user_point_charge_title": "Charging Point", "user_point_settlement_history": "Points Settlement History", "user_point_income_status": "Income Point Status", "user_point_withdrawal_points": "Withdrawal Points", "user_point_payments": "Point payments", "user_point_payment_order_id": "Order ID", "user_point_payment_error_code": "Error Code", "user_point_payment_error_msg": "Error Message", "user_point_payment_charge_point": "Charge Point", "user_point_payment_amount": "Payment Amount", "user_point_payment_key": "Payment Key", "user_point_payment_method": "Payment method", "user_point_payment_charge_title": "Charging Point Payment", "user_point_payments_dedicated": "Payments", "user_point_payment_msg_failed": "Point payment failed", "user_point_payment_msg_success": "Point payment success", "user_point_payment_msg_cancel": "Point payment cancel", "user_point_payment_msg_point_required": "Charge points is required", "user_point_payment_msg_point_range": "Points can be recharged from 20,000 to 1,000,000,000.", "user_point_payment_msg_method_required": "Please select a payment method_", "user_point_payment_msg_ready_failed": "Payment request failed", "user_point_payment_msg_approval_failed": "Payment approval request failed", "user_point_payment_msg_invalid": "Unauthorized access.", "user_point_payment_msg_amount": "※The final payment amount is the price with the dollar exchange rate applied.", "user_point_payment_msg_comment": "You can check your payment details in the point status.", "user_point_payment_method_card": "CARD", "user_point_payment_method_acct": "ACCT", "user_point_payment_method_vacct": "VACCT", "user_point_payment_status_progress": "In Progress", "user_point_payment_status_done": "Done", "user_point_payment_status_failed": "Failed", "user_point_payment_status_canceled": "Cancel", "user_point_payment_status_expired": "Expired", "user_point_payment_status_partial_canceled": "Partial Canceled", "user_point_settlement_title": "Point Settlement", "user_point_settlement_all": "Total points", "user_point_settlement_withdraw_available": "Available Withdrawal Point", "user_point_settlement_withdraw": "Withdrawal Point", "user_point_settlement_amount": "Settlement Amount", "user_point_withdraw": "Withdrawal Points", "user_point_tax_deduction": "Tax Deduction", "user_point_amount": "Amount", "user_point_settlement_req_point": "Request Points", "user_point_settlement_withdrawal_amount": "<PERSON><PERSON><PERSON> Amount", "user_point_settlement_msg_point_range": "Settlement Points Possible points range from 1 to {0}.", "user_point_settlement_msg_point_zero": "You don't have any redeemable points.", "user_point_settlement_msg_request": "Do you want to execute the settlement request?", "user_point_settlement_msg_request_failed": "Settlement request failed.", "user_point_refund_msg_bank_account": "Please check your bank account information.", "user_point_refund_request": "Request Point Refund", "user_point_refundable": "Refundable Points", "user_point_refund_req_point": "Refund Request Points", "user_point_refund_point": "Refund Points", "user_point_refund_fee": "Refund Fee", "user_point_refund_withdrawal_amount": "<PERSON><PERSON><PERSON> Amount", "user_point_refund_msg_point_zero": "You don't have any refund points.", "user_point_refund_msg_point_range": "Refund Points Possible points range from 1 to {0}.", "user_point_refund_msg_request_failed": "Refund request failed.", "user_point_refund_msg_execute_request": "Would you like to execute a refund request?", "user_notifications_title": "Notifications", "user_notifications_view_details": "View Details", "user_notification_history_info": "Notification history is displayed for up to the last 90 days.", "user_notifications_view_list": "View List", "user_notification_detail_title": "[gcube] Notice of Changes to Terms of Service & Privacy Policy", "user_notification_detail_intro": "We would like to inform you that some of the Terms of Service will be changed as follows.", "user_notification_detail_changes_title": "1. Summary of changes", "user_notification_detail_change_item1": "Change the currency based on points (1,000P = $1 → 1P = ₩1)", "user_notification_detail_change_item2": "Distinction between paid/free points and profit points", "user_notification_detail_change_item3": "Free points validity period applies", "user_notification_detail_change_item4": "Added Point Refund, Refund, and Settlement Policy", "user_notification_detail_change_item5": "Change of personal information processing consignment company (SPC → Toss payments)", "user_notification_detail_view_details": "View detailed changes", "user_notification_detail_effective_date": "2. Effective Date: April 21, 2025", "user_notification_detail_objection": "If you do not agree with the change, you may withdraw your membership or file an objection through the customer center. If no objection is filed during the notice period, you will be deemed to have agreed to the change and the new terms will apply.", "user_notification_detail_footer": "We will do our best to provide better service.\n\nThank you.", "user_notification_2_detail_title": "[gcube] Notice of change in point unit", "user_notification_2_detail_intro": "Hello, this is gcube.\nAs announced, we would like to inform you that the conversion standard of gcube points has been changed and the points you hold will be changed.\nThe conversion standard currency of points when recharging and redeeming will be changed from dollars to won, and the use value will not change even if the points are changed.\nThis is a reorganization to eliminate the inconvenience of calculating and checking points at the exchange rate when using points in the past.\nWe look forward to your use.", "user_notification_2_detail_1_changes_title": "Example of point changes", "user_notification_2_detail_1_change_item1": "Points currently held: P 1,000", "user_notification_2_detail_1_change_item2": "Current amount of points held in KRW: P 1,435", "user_notification_2_detail_1_change_item3": "Change holding points: P 1,435", "user_notification_2_detail_1_change_item4": "Amount in KRW of change holding points: P 1,435", "user_notification_2_detail_2_changes_title": "Date of point change", "user_notification_2_detail_2_change_item1": "April 21, 2025", "user_notification_2_detail_2_change_item2": "* Example change when the base exchange rate is 1435 won", "user_notification_2_detail_2_change_item3": "* For points issued for free, the exchange rate will not be applied to the free points and the points will remain as issued .", "user_notification_2_detail_objection": "For members with paid points, detailed point changes will be sent via email. \n If you have any questions about the changed points, please feel free to contact us via KakaoTalk on the homepage\nContact us or email.", "user_notification_2_detail_footer": "gcube will always do our best to provide you with better service.\n\nThank you.", "user_notifications_delete_error_msg": "Can't delete notifications.", "pricing_title": "Cloud GPU Price Comparison", "pricing_msg": "Here's an overview of the different GPU models and their price range across various cloud providers:", "pricing_updated": "Updated", "pricing_onrequest": "On Request", "pricing_hr": "hr", "session_expire_title": "<PERSON><PERSON>", "session_expire_description": "Your authentication has expired.", "session_extends_description1": "It's <span className=\"font-bold text-danger\">{0}</span> before the end of the session.", "session_extends_description2": "Do you want to extend the session time?", "session_extends_description3": "You can adjust individual session times in your personal settings.", "auth_terms": "Terms of Service", "auth_privacy": "Privacy Policy", "auth_marketing": "Consent to receive marketing information", "auth_signin": "Sign In", "auth_signup": "Sign Up", "auth_contactus": "Contact Us", "signin_title": "GCUBE LOGIN", "signin_desc": "Login GCUBE and create nodes and workloads.", "signin_but_google": "Login with Google", "signin_but_naver": "Login with Naver", "signin_but_microsoft": "Login with Microsoft", "signin_msg_failed": "<PERSON><PERSON> failed", "signin_msg_already": "This e-mail address has already been registered.", "signup_title": "GCUBE SIGN UP", "signup_terms_title": "Terms and Conditions", "signup_input_title": "Enter user information", "signup_desc": "Join GCUBE and create nodes and workloads.", "signup_but_google": "Sign up with Google", "signup_but_microsoft": "Sign up with Microsoft", "signup_but_naver": "Sign up with <PERSON><PERSON>", "signup_terms_required": "Required", "signup_terms_optional": "Optional", "signup_terms_viewall": "View All", "signup_terms_agreeall": "Agree with all terms and conditions", "signup_terms_agreeall_desc": "Agree to all of the following terms and conditions of gcube: Terms of Service; Privacy Policy;", "signup_label_email": "E-Mail", "signup_label_name": "User Name", "signup_label_phone": "Cell phone", "signup_label_phone_desc": "Cell phone: 00000000000", "signup_label_company": "Company Name", "signup_label_industry_desc": "ex): Software development and vendors/developers", "signup_label_position": "Job and Position", "signup_label_position_desc": "ex): Head of Data Alliance Operations Planning/Principal Investigator", "signup_msg_name_required": "Name is required.", "signup_msg_phone_required": "Cell phone is required.", "signup_msg_phone_invalid": "Cell phone format is incorrect.", "signup_msg_failed": "User registration failed", "signup_msg_success": "User registration success", "singup_input_description01": "Finally, please fill in the information below. ", "singup_input_description02": "We'll provide you with updates and event news to help you use gcube.", "unauthorized_msg01": "Login Session Has Expired", "unauthorized_msg02": "Click the button below to be redirected to the login page.<br />We apologize for any inconvenience.", "unauthorized_agent_msg01": "Agent authentication failed.", "unauthorized_agent_msg02": "Click the button below to be redirected to the login page.<br />We apologize for any inconvenience.", "forbidden_msg01": "The resource cannot be accessed.", "forbidden_msg02": "Authentication is required to access this resource", "landing_menu_pricing": "Pricing", "landing_menu_docs": "Docs", "landing_menu_faq": "FAQ", "landing_menu_blog": "Blog", "landing_footer_da": "Data-Alliance.inc", "landing_footer_ceo": "gcube CEO <PERSON><PERSON><PERSON><PERSON><PERSON>", "landing_footer_brn": "Business registration number 415-81-56947", "landing_footer_mon": "Mail order number 2022-서울강남-0448", "landing_footer_address": "484, Gangnam-daero, Gangnam-gu, Seoul, 06120 KOREA", "index_msg01": "GPU Cloud platform<br/> with max 70% cheaper", "index_msg02": "GPUaaS is the new choice!", "index_msg03": "Create more value by sharing GPU resources.", "index_msg031": "gcube is a platform that shares idle GPU resources around the world, providing up to 70% cheaper cloud computing services for GPU demanders and additional monetization opportunities for GPU holders <br/> thereby maximizing mutual benefits between providers and demanders.", "index_msg032": "Apply for<br/>AI service", "index_msg04": "Efficient and competitive service", "index_msg05": "Welcome to the beta test where you can experience the new world", "index_msg06": "※Maximum monthly profit forecast<br class=\"py-3\">※Profit can change by the amount of supply", "index_msg07": "Share GPU and create profit", "index_msg08": "Connect your own idle GPU with gCube and create a consistent additional monthly income, max $178", "index_msg09": "How to share GPU", "index_msg10": "※The price is based on the minimum scale", "index_msg11": "Save your AI service operation cost", "index_msg12": "Compute only when necessary and save your cost,<br /> max 70% monthly", "index_msg13": "Detailed price table", "index_msg14": "Transform your cloud computing<br />service through innovative use of<br />GPU resources.", "index_msg15": "gCube is a GPU shared economy service that provides cloud computing service with powerful performance and cheaper price, through global GPU network.", "index_msg16": "※GPUs for data center(T4, V100, A100) are max 50% cheaper than other service providers.<br class='py-3'>※Total cost, including basic price, does not exceed 100%", "index_msg17": "Price policy for cloud computing usage", "index_msg18": "You can save max 70% cost with low usage, compared with other services. ‘Pay-as-you-go’ system enable you to use cloud computing service with competitive price, in case of max usage.", "index_msg19": "The most critical problem of GPU cloud service is the cost.", "index_msg20": "With our own cloud native technology, we can provide customized and reliable service by combining the GPUs of CSP(Cloud Service Provider)s and the GPUs of participating individuals, dealing with various service environments, such as many concurrent connections.", "index_msg21": "By connecting idle GPUs around the world, gCube provides high-performance cloud computing service with excellent scalability for AI service providers and the participating GPU resource providers now have a chance for additional profit.", "index_msg22": "Large-scale data processing", "index_msg23": "Technical advantage", "index_msg24": "Utilize the powerful computing power of GPU", "index_msg25": "Utilize the powerful computing power of GPU to quickly process tasks such as machine learning model training, <br />deep learning, and data analysis.", "index_msg26": "Easy to deploy and manage applications", "index_msg27": "You can flexibly respond to workloads and easily deploy and manage applications.", "index_msg28": "Serverless environment", "index_msg29": "You can run various cloud platforms or services in the same way in a serverless environment.", "index_msg30": "Classify GPU suppliers by Tier 1 to provide optimal services tailored to the QoS requirements of AI service companies.", "index_msg31": "Max 70% cheaper service", "index_msg32": "Cost optimizing solutuon", "index_msg33": "You can automatically expand your service by calculating the optimal cost based on Service Level Agreement (SLA)", "index_msg34": "Convenient operation efficiency", "index_msg35": "100% compatible container with legacy services", "index_msg36": "We provide complete manageable container service that a participating individual can use GPUs in the shared network and major cloud services at the same time.", "index_msg37": "Proven clarity with international patents", "index_msg38": "Clear compensation by share contribution", "index_msg39": "We clearly manage computing resource usage by utilizing block chain technology and provide participating individuals with compensation by contribution rate, based on international patents", "index_msg40": "Voices of Alpha Testers", "index_msg41": "We share the thoughts and opinions of those who helped us as testers during the process of preparing the service.", "index_msg42": "BLOG", "index_msg43": "Check out the latest news and industry trends.", "index_msg44": "gCube’s Partners", "index_msg45": "gcube is expanding its global partner network with the investment from Naver Cloud, <br />as well as with strategic business alliance with the company.", "index_msg46": "Improve productivity and efficiency", "index_msg47": "Ask questions now", "index_msg48": "Want to learn how gcube can help you?", "index_msg49_01": "Cloud providers", "index_msg49_02": "NAVER CLOUD PLATFORM, AWS, Azure,<br /> NHN Cloud, KT Cloud", "index_msg49_03": "Uninterrupted supply and use of GPU resources around the clock", "index_msg50_01": "Dedicated servers", "index_msg50_02": "Cloud providers, data centers, <br />and PC Room", "index_msg50_03": "-Provide GPU servers via contract <br />(in the form of gcube servers)", "index_msg50_04": "Uninterrupted supply and use of GPU resources around the clock", "index_msg51_01": "Interruptible way", "index_msg51_02": "PC Room,Personal", "index_msg51_03": "- GPU Resources Optimized for Cost Savings <br/>with Provider-Customized GPU Sharing<br />", "index_msg51_04": "Supply and use GPU resources when you want, where you want.", "index_msg40_01": "Sound Mind", "index_msg40_02": "One of the most important considerations for companies providing AI services is the computing costs incurred during AI training and inference. At Sound Mind, we are currently switching to gcube as the first tester of the gcube service, as the monthly cloud costs for learning and inference have become a burden. Although we have our own equipment, we were able to earn additional revenue by sharing GPUs during the idle time of that equipment, and we experienced a 55% reduction in computing operating costs with gcube. Translated with DeepL.com (free version)", "index_msg40_03": "PC Room", "index_msg40_04": "Our 1% PC Room has 8 PCs running 24/7 on a fixed basis for testing. Seeing the PCs running during idle hours when there are no customers made me feel lighter. I think it's worth trying gcube if it can at least offset some of the operating costs, even if it's electricity or nighttime labor. I think many PC cafes around the country will switch to sharing GPUs on gcube and earn extra revenue in the future. At 1% PC Cafe, we plan to do this with all of our PCs in the near future. I don't speak for PC cafes, but I think it's a great opportunity for PC cafes to earn additional revenue without investing in additional equipment.", "index_msg40_05": "Backend developers", "index_msg40_06": "I am <PERSON><PERSON><PERSON>, who participated as an alpha tester of the GCUBE service. GCUBE is different from other cloud services in that it charges differently based on usage. As a developer, cost is a very sensitive area, so I think it was more important to me. Of course, during the testing process, there were bugs and sometimes unpredictable results, but after witnessing the passionate response and continuous improvement efforts of the GCUBE team, I have great expectations for the GCUBE service. I am really looking forward to seeing how the GCUBE service will develop and change in the future.", "gpu_top_msg00": "Enjoy the new source of<br /> revenue with your idle GPUs", "gpu_top_msg01": "Install gCube agent, share your idle GPU any time and create profit", "gpu_body_msg01": "It’s very simple<br />Download Agent", "gpu_body_msg02": "Run the Agent", "gpu_body_msg03": "Check out GPU share status and<br /> your profit", "gpu_body_msg04": "GPU share is always available_ But there are<br /> hardware requirements for seamless service.", "gpu_body_msg05": "Please check out the below specifications for the maximum performance and profit", "gpu_body_msg06": "Basic PC installation specifications", "gpu_body_msg07": "Recommended specifications", "pricing_msg01": "Want to reduce GPU spending?<br />Get started now~", "pricing_msg02": "Pay only for what you use with up to 70% lower basic rates.<br />Pricing innovation in GPU shared cloud starts with GCube.", "pricing_msg03": "You can choose GPU, vRAM, vCPU, STORAGE, RAM according to your desired specifications, <br />and use it at a competitive price compared to other companies even at maximum utilization.", "pricing_msg04": "<PERSON><PERSON><PERSON><PERSON>", "pricing_msg05": "※Rates may vary depending on time.", "pricing_msg06": "Network fees", "pricing_msg07": "Network usage for outbound traffic is charged according to the following rate table.", "pricing_msg08": "※The exchange rate is based on the final exchange rate announced by a credible institution the previous month, and is applied from the 1st of the month of use.", "pricing_msg09": "※The exchange rate fluctuates monthly, and the same exchange rate applies from the 1st to the last day of each month.", "pricing_msg10": "Contact us now.", "pricing_msg11": "Need help? Contact our support team for quick, personalized help with your questions and concerns.", "pricing_msg12": "Cloud Provider", "pricing_msg13": "Bare Metal Server", "pricing_msg14": "Personal, PC Room", "pricing_table_th01": "Minimum Price /hr", "pricing_table_th02": "Based on usage base", "pricing_table_th03": "Category", "pricing_table_th04": "Usage category", "pricing_table_th05": "Rate (per GB)", "pricing_table_th06": "Maximum price /hr", "pricing_table_td01": "Inbound traffic", "pricing_table_td02": "Same for all segments", "pricing_table_td03": "Outbound traffic<br/>(traffic using the Internet)", "pricing_table_td04": "Inbound and outbound traffic", "pricing_table_td_le": "{0} or less", "pricing_table_td_range": "Over {0} ~ {1} or less", "pricing_table_td_gt": "Over {0}", "pricing_table_min": "Minimum", "faq_msg01": "Check out the gcube FAQ below.", "faq_msg02": "FAQs", "contactus_msg01": "gcube is <br/>looking forward to connecting with you.", "contactus_msg02": "Please feel free to tell us anything you want to know about our services. <br/>We will tell you more about gcube by phone or email.", "contactus_msg03": "What kind of AI models can I create with gcube?", "contactus_msg04": "How do I pay for gcube?", "contactus_msg05": "How can I earn money with gcube?", "contactus_label_familyname": "Family name", "contactus_label_name": "Name", "contactus_label_email": "Business Email", "contactus_label_country": "Country/Region", "contactus_label_company": "Company name", "contactus_label_phone": "Phone number", "contactus_label_help": "How can we help you?", "contactus_register_msg_familyname_required": "Please enter your last name.", "contactus_register_msg_name_required": "Please enter your first name.", "contactus_register_msg_email_required": "Please enter your work email.", "contactus_register_msg_country_required": "Please select your country.", "contactus_register_msg_company_required": "Please enter your company name.", "contactus_register_msg_phone_required": "Please enter your phone number.", "contactus_register_msg_inquiry_required": "Please enter your inquiry.", "contactus_register_msg_email_invalid": "Invalid email format.", "contactus_register_msg_phone_invalid": "Invalid phone number format.", "contactus_register_msg_send_email": "An inquiry email has been sent.", "qna_msg_title01": "* You can get faster answers from the FAQ.", "qna_msg_title02": "* If you cannot find an answer, please use the inquiry form to contact us directly.", "qna_register_msg_type": "Inquiry Type", "qna_register_msg_name": "Name", "qna_register_msg_title": "Title", "qna_register_msg_content": "Content", "qna_register_msg_target": "Inquiry Target", "qna_register_msg_target_workload": "Workload", "qna_register_msg_target_node": "Node", "qna_register_msg_send_qna": "Submit Inquiry", "qna_register_msg_kakao_chat": "KakaoTalk Chat", "qna_register_msg_type01": "Account", "qna_register_msg_type02": "Personal Verification", "qna_register_msg_type03": "Node", "qna_register_msg_type04": "Workload", "gate_node_msg01": "<span style=\"color: #8247ff\">Share your GPU</span> and<br>earn revenue.", "gate_node_msg02": "Connect your idle GPUs to gcube and<br>earn stable additional revenue of up to $178 per month.", "gate_workload_msg01": "<span style=\"color: #8247ff\">Save on AI</span> service operating costs.", "gate_workload_msg02": "Compute as much time and scale as you need,<br>and save up to 70% per month.", "http_bad_request": "Bad request", "http_bad_request_msg": "Sorry, this page is currently unavailable. <br/>Click the button below to move the page.", "http_internal_server_error": "System Errors", "http_internal_server_error_msg": "Sorry, the current page cannot handle the information you requested. <br/> Please click the button below to move the page.", "ws_disconnected_error_msg": "The WebSocket connection has been lost.<br /> Please try again in a moment.", "ws_exit_msg": "Container Terminal has exited.", "personal_setting_title": "Setting", "personal_setting_session_time": "Session Time", "personal_setting_30min": "30 minutes", "personal_setting_60min": "60 minutes", "personal_setting_90min": "90 minutes", "personal_setting_120min": "120 minutes", "main_banner_01_msg01": "Announcing NAVER Cloud Marketplace", "main_banner_01_msg02": "Lowest price<br/> GPU package for AI", "main_banner_01_msg03": "Breaking the myths of AI GPU rental costs.", "main_banner_01_msg04": "Speed up development, lower costs", "main_banner_01_msg05": "If it was difficult to verify AI due to the price burden<br/>now, use it freely without worrying about the cost and make better services<br/>gcube will be with you for successful services", "main_banner_02_msg01": "Creative Economy Innovation Center Celebrates Technopark Agreement", "main_banner_02_msg02": "Free<br/> PoC support up to the latest RTX5090", "main_banner_02_msg03": "GPUs of your choice from the latest GPUs up to <br/> gcube for free.", "main_banner_02_msg04": "Companies that need technology verification through various GPUs, developers who have developed AI but<br/> cannot proceed with the desired PoC due to lack of GPUs, etc,", "main_banner_03_msg01": "Global GPU Grid", "main_banner_03_msg02": "Up to 70% economical<br/>GPU Cloud platform", "main_banner_03_msg03": "Provides AI infrastructure for everyone at a reasonable GPU usage <br/>cost.", "main_banner_03_msg04": "gcube shares the world's idle GPUs, providing <br/>up to 70% more affordable cloud computing services for GPU users and additional monetization opportunities for GPU providers."}}