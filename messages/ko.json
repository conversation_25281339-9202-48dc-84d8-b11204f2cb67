{"i18nData": {"title": "GCUBE AI PLATFORM", "menu_pricing": "GPU 가격 비교", "menu_dashboard": "대쉬보드", "menu_nodes": "노드", "menu_workloads": "워크로드", "menu_profile": "내 프로필", "menu_point": "포인트 현황", "menu_point_charge": "포인트 충전", "menu_point_settlement": "포인트 정산", "menu_logout": "로그아웃", "menu_docs": "문서", "menu_personal_setting": "개인 설정", "menu_personal_payment": "결제내역", "menu_personal_storage": "개인 저장소", "but_more": "더보기", "but_more_gpu": "GPU 더보기", "but_logout": "로그아웃", "but_ok": "확인", "but_cancel": "취소", "but_close": "닫기", "but_edit": "편집", "but_search": "검색", "but_delete": "삭제", "but_selected_delete": "선택 삭제", "but_modify": "수정", "but_deploy": "배포", "but_monitoring": "모니터링", "but_start": "실행", "but_stop": "중지", "but_register": "등록", "but_receipt": "영수증", "but_history": "내역", "but_payment": "결제", "but_payment_retry": "결제재시도", "but_payment_approval": "결제승인", "but_payment_finish": "결제완료", "but_settlement_withdrawal_request": "출금신청", "but_register_node": "새 노드 연결", "but_register_workload": "새 워크로드 등록", "but_set_price": "가격설정", "but_income_history": "수입내역", "but_spend_history": "지출내역", "but_cloud_edit": "클라우드 정보", "but_history_details": "세부내역", "but_container_logs": "컨테이너 로그", "but_container_terminal": "컨테이너 터미널", "but_container_ssh": "컨테이너 SSH", "but_container_event": "파드 이벤트", "but_container_deploy_event": "배포 이벤트", "but_register_credential": "새 사용자인증정보 등록", "but_point_charge": "포인트 충전", "but_point_settlement": "포인트 정산", "but_point_all": "전액", "but_signup": "회원가입", "but_signin": "로그인", "but_retry_login": "다시 로그인", "but_next": "다음", "but_profile_edit": "프로필 수정", "but_contactus": "문의하기", "but_register_pod_ssh": "접속정보 등록", "but_delete_pod_ssh": "접속정보 삭제", "but_search_pod_ssh_publicIP": "공인IP 조회", "but_session_extends_setting": "세션 설정", "but_other_repo": "다른 저장소 선택", "but_register_credential_info": "인증 정보 등록", "but_settlement_request": "정산 요청", "but_point_refund_request": "포인트 환급 신청", "but_point_used": "사용설정", "but_node_taint_event_list": "노드 차단 이력", "but_dedicated_workload_status": "지정 노드 현황", "but_worklaod_operational_status": "운영현황", "but_stop_watching_today": "오늘 그만 보기", "dialog_title_ok": "확인", "dialog_title_waring": "경고", "dialog_title_error": "오류", "dialog_title_notice": "공지", "msg_confirm_logout": "로그아웃 하시겠습니까?", "msg_desc_logout": "현재 세션을 종료하려면 아래의 \"로그아웃\"버튼을 클릭하세요.", "msg_no_content": "조회된 컨텐츠가 없습니다.", "resource_gpu": "GPU", "resource_gpu_memory": "GPU 메모리", "resource_cpu": " CPU", "resource_memory": "메모리", "resource_disk": " 디스크", "resource_network": "네트워크", "resource_gpu_usage": "GPU 사용량", "resource_gpu_memory_usage": "GPU 메모리 사용량", "resource_cpu_usage": "CPU 사용량", "resource_memory_usage": "메모리 사용량", "resource_disk_usage": "저장소 사용량", "resource_disk_occu": "저장소 사용량", "resource_network_traffic": "네트워크 트레픽", "resource_network_traffic_in": "네트워크 받은패킷", "resource_network_traffic_out": "네트워크 보낸패킷", "resource_network_speed": "네트워크 속도", "cloud_ncp": "네이버 클라우드", "cloud_aws": "아마존 웹 서비스", "cloud_gcp": "구글 클라우드", "cloud_azure": "마이크로소프트 AZURE", "cloud_lambda": "람다 클라우드", "repo_awsecr": "Amazon ECR", "repo_dockerhub": "도커허브", "repo_nvidia": "엔비디아", "repo_github": "깃허브", "repo_redhat": "레드햇 Quay", "repo_huggingface": "허깅페이스", "form_label_select_time": "시간 범위 선택", "form_label_customize_time": "사용자 시간 범위", "form_label_start_time": "시작시간", "form_label_end_time": "종료시간", "form_label_interval": "샘플링 간격", "form_select_options_interval_1min": "1 분", "form_select_options_interval_2min": "2 분", "form_select_options_interval_3min": "3 분", "form_select_options_interval_5min": "5 분", "form_select_options_interval_10min": "10 분", "form_select_options_interval_15min": "15 분", "form_select_options_interval_1hour": "1 시간", "form_select_options_interval_2hour": "2 시간", "form_select_options_interval_3hour": "3 시간", "form_select_options_interval_5hour": "5 시간", "form_select_options_time_30min": "마지막 30 분", "form_select_options_time_1hour": "마지막 1 시간", "form_select_options_time_2hour": "마지막 2 시간", "form_select_options_time_3hour": "마지막 3 시간", "form_select_options_time_5hour": "마지막 5 시간", "form_select_options_time_8hour": "마지막 8 시간", "form_select_options_time_12hour": "마지막 12 시간", "form_select_options_time_1day": "마지막 1 일", "form_select_options_time_2day": "마지막 2 일", "form_select_options_time_3day": "마지막 3 일", "form_select_options_time_7day": "마지막 7 일", "table_th_date": "날짜", "table_th_hour": "시간", "table_th_node": "노드", "table_th_workload": "워크로드", "table_th_pod": "파드", "table_th_point": "포인트", "table_th_point_income": "수입 포인트", "table_th_point_spend": "지출 포인트", "table_th_amount": "금액", "table_th_amount_unit": "기본 금액", "table_th_amount_usage": "이용 금액", "table_th_gpu": "GPU", "table_th_gpu_name": "GPU 이름", "table_th_gpu_count": "GPU 개수", "table_th_gpu_memory": "GPU 전체 메모리", "table_th_cpu_count": "CPU 개수", "table_th_memory": "메모리", "table_th_disk": "저장소", "table_th_price": " 시간당 가격", "table_th_price_unit": "기본요금 / hr", "table_th_price_usage": "사용기반에 따른 요금 / hr", "table_th_update_time": "설정일시", "table_th_use_time": "이용시간", "table_th_gpu_usage": "GPU 이용률", "table_th_gpu_memory_usage": "GPU 메모리 이용률", "table_th_cpu_usage": "CPU 이용률", "table_th_memory_usage": "메모리 이용률", "table_th_disk_usage": "저장소 이용률", "table_th_network_usage": "네트워크 이용량", "table_th_payment_time": "결제일시", "table_th_payment_method": "결제유형", "table_th_payment_currency": "통화", "table_th_payment_amount": "결제금액", "table_th_payment_charge_point": "충전포인트", "table_th_payment_status": "상태", "table_th_payment_receipt": "영수증", "table_th_product_name": "제품이름", "table_th_gpu_model": "GPU 모델", "table_th_pod_event_res_type": "응답 유형", "table_th_pod_event_type": "이벤트 유형", "table_th_pod_event_reason": "이벤트 이유", "table_th_pod_event_component": "보고 구성 요소", "table_th_pod_event_kind": "이벤트 대상 유형", "table_th_pod_event_manager": "워크플로 식별자", "table_th_pod_event_operation": "작업 유형", "table_th_pod_event_count": "발생횟수", "table_th_pod_event_create_at": "최초발생일자", "table_th_pod_event_last_at": "최근발생일자", "table_th_pod_event_detailed": "상세보기", "table_th_description": "설명", "table_th_balance_after_settlement": "정산 후 잔액", "table_th_requested_point": "요청 포인트", "table_th_settlement_point": "정산 포인트", "table_th_tax_deduction": "세금 공제(3.3%)", "table_th_exchange_rate": "환율", "table_th_with_or_without_settlement": "정산 유무", "table_th_request_date": "요청일", "table_th_settlement_date": "정산일시", "table_th_transfer_points": "실 지급 포인트", "table_th_charge_type": "충전유형", "table_th_charge_points": "충전포인트", "table_th_charge_balance": "잔액", "table_th_charge_date": "충전일시", "table_th_charge_used": "사용유무", "table_th_refund_balance": "환급 후 잔액", "table_th_refund_points": "환급포인트", "table_th_refund_transfer_points": "실지급 포인트", "table_th_refund_fee": "수수료", "table_th_refund_status": "환급유무", "table_th_refund_request_date": "요청일", "table_th_refund_time": "환급일시", "table_th_node_name": "노드명", "table_th_node_gpu_model": "GPU 모델명", "table_th_node_gpu_memory": "GPU 메모리", "table_th_node_gpu_driver": "그래픽 드라이버", "table_th_node_gpu_cuda": "CUDA Toolkit", "table_th_node_hostspec": "장치정보", "table_th_node_state": "실행상태", "table_th_node_connected_status": "연결상태", "table_th_node_state_history": "상태이력", "table_th_node_taint": "상태", "table_th_node_taint_type": "구분", "table_th_node_taint_content": "오류 발생 현황", "table_th_node_taint_turationAt": "오류 지속 시간", "table_th_node_taint_cancelAt": "발생일시", "table_th_node_taint_settingAt": "해제일시", "table_td_node_state_detail": "이력보기", "table_td_sum": "합계", "select_cuda_version": "버전 선택", "node_no_content": "연결된 노드가 없습니다.", "node_status_open": "미개통", "node_status_provision": "개통", "node_status_running": "실행", "node_status_fail": "실패", "node_status_stop": "중지", "node_status_created": "생성", "node_label_category": "노드 유형", "node_label_gpuspec": "GPU 정보", "node_label_hostspec": "장치 정보", "node_label_driver": "그래픽 드라이버", "node_label_cuda": "CUDA Toolkit", "node_label_createdat": "생성 일시", "node_label_lastseen": "마지막 업데이트 일시", "node_label_income": "수입", "node_label_unitprice": "기본단가", "node_label_usageprice": "이용단가", "node_label_name": "이름", "node_label_address": "주소", "node_label_state": "상태", "node_label_substate": "하위 상태", "node_label_shared": "공유여부", "node_label_vpnpubkey": "VPN 공개키", "node_label_cloud_instid": "인스턴스 아이디", "node_label_cloud_region": "지역", "node_label_cloud_vmip": "VM IP 주소", "node_label_cloud_unitprice": "단가", "node_register_title": "노드등록", "node_register_step1_title": "다운로드 GCUBE 에이전트 프로그램", "node_register_step1_msg": "GCUBE 에이전트를 통해서 새로운 장치를 연결하세요.", "node_register_step2_title": "설치사양", "node_register_step2_msg": "설치 기본 사양을 확인해보세요.", "node_register_step2_minspec": "최소 사양", "node_register_step2_recspec": "권장 사양", "node_register_step2_basicspec": "기본 사양", "node_register_step3_title": "에이전트 실행", "node_register_step3_msg": "새 장치를 인증하고 실행합니다.", "node_info_title": "노드 정보", "node_info_node": "노드", "node_info_devicespec": "장치 스펙", "node_info_cloud": "클라우드", "node_monitoring_title": "노드 모니터링", "node_delete_title": "노드삭제", "node_delete_msg_confirm": "해당 노드를 삭제 하시겠습니까?", "node_delete_pc_msg_confirm": "Agent를 이용하여 삭제 하세요.<br/> 삭제 실패 시 플랫폼의 삭제 기능을 이용 하세요.", "node_delete_msg_failed": " 노드삭제 실패", "node_state_change_title": "노드 상태 변경", "node_state_change_msg_confirm": "해당 노드를 {0} 하시겠습니까?", "node_state_change_msg_failed": " 노드 {0} 실패", "node_cloud_edit_title": "클라우드 파라미터", "node_cloud_ncp_edit_title": "네이버 클라우드 파라미터", "node_cloud_nhn_edit_title": "NHN 클라우드 파라미터", "node_cloud_edit_failed": "편집 실패", "node_cloud_edit_invalid_json": "JSON 형식이 아닙니다.", "node_setprice_title": "노드 가격 설정", "node_setprice_history": "가격 설정 내역", "node_setprice_price_unit": "기본 요금", "node_setprice_price_usage": "이용 요금", "node_setprice_price_hour": "시간당", "node_setprice_price_unit_hour": "시간당 기본 단가", "node_setprice_price_usage_hour": "시간당 이용 단가", "node_setprice_msg_price_required": "시간당 단가를 입력하세요.", "node_setprice_msg_price_range": "요금은 {0}보다 크고 {1}보다 작아야 합니다.", "node_setprice_msg_price_same_value": "기존 값과 같습니다.", "node_setprice_msg_failed": "가격설정 실패", "node_setprice_msg_running_pod": "실행중인 파드가 있어 가격 설정을 할 수 없습니다.", "node_setprice_msg_confirm": "다음과 같은 가격으로 설정 하시겠습니까?", "node_setprice_msg_no_content": "설정된 가격 정보가 없습니다.", "node_setprice_desc01": "시간 당 기본요금과 시간당 이용요금의 조정 범위는 선정된 기본 단가의 75% 까지만 설정할 수 있습니다.<br>환율은 이용 월에 따라 적용됩니다.", "node_setprice_desc02": "※ 요금은 시간에 따라 변동될 수 있습니다.", "node_setprice_desc03": "※ 설정된 가격은 GPU 이용 단가 변경 시 설정된 비율에 따라 조정되어질 수 있습니다.", "node_setprice_unitprice_desc01": "현재 설정 가격 : 이용 금액의 {0}%", "node_setprice_unitprice_desc02": "설정 가능 가격 : 0원 ~ {0}원", "node_setprice_usageprice_desc01": "현재 설정 가격 : 이용 금액의 {0}%", "node_setprice_usageprice_desc02": "설정 가능 가격 : 0원 ~ {0}원", "node_income_history_title": "수입 내역", "node_income_history_details_title": "수입 세부 내역", "node_state_taint_grpc_msg": "통신이 원활하지 않습니다.<br /> 네트워크를 확인하세요", "node_state_taint_gpuspec_msg": "Agent에서 GPU 인식에 실패하였습니다.<br />최신 그래픽 드라이버를 설치하시고,<br /> gcube Agent에서 그래픽 드라이버 재설치 과정을 수행하십시오. <br />(해결이 안 될 경우 문의 바랍니다.)", "node_state_taint_vast_msg": "V.rented", "node_state_taint_speed_msg": "네트워크 속도가 원활하지 않습니다.<br /> 네트워크를 확인하세요", "node_state_taint_syml_msg": "컨테이터 저장소 오류<br />기술 지원을 요청 하십시요.", "node_state_taint_diskfull_msg": "디스크 용량 부족<br />디스크 용량을 하십시요.", "node_no_register_gpu": "GCUBE 모델에 등록되지 않은 GPU 모델 입니다.", "workload_no_content": "등록된 워크로드가 없습니다.", "workload_status_deploy": "배포", "workload_status_finish": "종료", "workload_status_open": "미배포", "workload_category_inference": "추론", "workload_category_learning": "학습", "workload_category_batch": "일괄 작업", "workload_label_overview": "개요", "workload_label_workload_number": "워크로드 번호", "workload_label_state": "상태", "workload_label_description": "설명", "workload_label_desc": "워크로드 설명", "workload_label_desc_placeholder": "설명을 입력해 주세요.", "workload_label_target_node": "목적노드", "workload_label_target_spec": "목적스펙", "workload_label_target_gpu": "GPU", "workload_label_target_gpu2": "대상  GPU", "workload_label_target_gpu_memory": "GPU 메모리", "workload_label_target_cuda": "최소 CUDA 버전", "workload_label_replica": "레플리카", "workload_label_price_max_hour": "최대 시간당 가격", "workload_label_createdat": "생성일시", "workload_label_deployat": "배포일시", "workload_label_closeat": "종료일시", "workload_label_cluster": "클러스터", "workload_label_service_port": "서비스 포트", "workload_label_service_url": "서비스 URL", "workload_label_category": "유형", "workload_label_container": "컨테이너", "workload_label_container_image": "컨테이너 이미지", "workload_label_container_command": "컨테이너 명령", "workload_label_container_envs": "컨테이너 환경변수", "workload_label_container_shared_memory": "공유 메모리", "workload_label_container_port": "컨테이너 포트", "workload_label_repo_auth_check": "저장소 인증", "workload_label_repo_auth_msg": "개인 저장소 인증시 사용(인증 정보는 프로필에서 등록 가능 합니다.)", "workload_label_direct_deploy": "즉시배포", "workload_label_storage": "영구 저장소", "workload_label_storage_capacity": "용량", "workload_label_storage_mount_path": "마운트 경로", "workload_label_storage_class": "클래스", "workload_label_repo_type": "저장소 유형", "workload_label_price_total": "총 지출", "workload_label_price_unit": "기본료", "workload_label_price_usage": "사용료", "workload_label_amount_total": "총 예상 금액", "workload_label_vat": "VAT 별도", "workload_label_options": "옵션", "workload_label_cuda": "CUDA", "workload_label_gpu_status": "GPU 상태", "workload_label_avilable_gpu": "사용 가능 GPU만 보기", "workload_label_image_verification": "이미지 검증", "workload_label_verification_complete": "검증완료", "workload_label_personal_storage": "개인저장소", "workload_label_refundable_points": "환급가능 포인트", "workload_label_payment": "결제", "workload_label_free": "무료", "workload_label_workload": "워크로드", "workload_label_network": "네트워크", "workload_label_refund": "지급", "workload_label_request": "요청", "workload_label_charge_payment": "결제충전", "workload_label_charge_free": "무료충전", "workload_label_charge_cancel": "충전취소", "workload_label_charge_point_used_setting": "포인트 사용 설정", "workload_msg_charge_point_used_setting": "포인트 {0}를 사용 설정 하시겠습니까?", "workload_msg_charge_point_used_failed": "포인트 사용 설정 실패", "workload_label_points_charge_history": "포인트 충전 내역", "workload_label_points_refund_history": "포인트 환급 내역", "workload_info_title": "워크로드 정보", "workload_info_deployment_status": "배포 상태", "workload_info_table_th_node": "노드", "workload_info_table_th_pod": "파드", "workload_info_table_th_status": "파드 상태", "workload_container_logs_title": "컨테이너 로그", "workload_container_terminal_title": "컨테이너 터미널", "workload_container_terminal_exit": "컨테이너 터미널이 종료되었습니다.", "workload_register_title": "워크로드 등록", "workload_register_msg_description_required": "설명을 입력하세요.", "workload_register_msg_repo_required": "저장소 유형을 선택하세요.", "workload_register_msg_container_image_required": "컨테이너 이미지 URL을 입력하세요.", "workload_register_msg_container_image_check_required": "컨테이너 이미지 URL을 검증하세요. ", "workload_register_msg_container_port_required": "컨테이너 포트를 입력하세요.", "workload_register_msg_gpu_required": "GPU를 선택하세요", "workload_register_msg_gpu_memory_required": "GPU 메모리를 입력하세요.", "workload_register_msg_replica_required": "레플리카를 입력하세요.", "workload_register_msg_price_max_required": "최대 시간당 가격을 입력하세요.", "workload_register_msg_failed": "워크로드 등록 실패", "workload_register_msg_deploy_failed": "워크로드 배포 실패", "workload_register_msg_payment_required": "포인트가 모두 소진 되었습니다.<br/>포인트 충전을 한 후 사용하세요.", "workload_register_msg_price_desc": "서비스를 선택하고 사용량을 입력하여 예상 사용요금을 산정해 보실 수 있습니다. 요금계산기에서 산출된 금액은 실제 정산 금액과 다른 예상 요금입니다.<br>환율은 이용 월에 따라 적용됩니다.", "workload_register_msg_shared_memory_required": "1GB 이상 {0}GB이하로 설정하세요.", "workload_modify_title": "워크로드 정보 수정", "workload_modify_msg_failed": "워크로드 정보 수정 실패.", "workload_delete_title": "워크로드 삭제", "workload_delete_msg_failed": "워크로드 삭제 실패", "workload_delete_msg_confirm": "해당 워크로드를 삭제 하시겠습니까?", "workload_state_change_title": "워크로드 {0}", "workload_state_change_msg_confirm": "해당 워크로드를 <span>{0}</span> 하시겠습니까 ?", "workload_state_change_msg_failed": "워크로드 <span>{0}</span> 실패", "workload_monitoring_title": "워크로드 모니터링", "workload_spend_history_title": "지출 내역", "workload_spend_history_details_title": "지출 세부 내역", "workload_form_node_any": "전체", "workload_form_node_tier1": "Tier 1", "workload_form_node_tier2": "Tier 2", "workload_form_node_tier3": "Tier 3", "workload_form_select_gpu": "GPU 선택", "workload_spend_total": "총 지출", "workload_point_payment_required": "포인트를 충전 하세요.", "workload_pod_events": "파드 이벤트", "workload_pod_deploy_events": "배포 이벤트", "workload_form_numberof_available_nodes": "사용가능 노드 수", "worklaod_operational_status": "운영현황", "worklaod_operational_status_detail": "운영 세부 내역", "worklaod_dedicated_node_list": "지정 노드 목록", "worklaod_dedicated_node_detail": "노드 세부 이력", "workalod_tooltip_workload_registered_gpu": "현재 워크로드에 등록된 GPU", "workalod_tooltip_gpu_available": "사용 가능", "workalod_tooltip_gpu_unavailable": "사용 불가능", "workload_containerImage_message_not_found_exposed_ports": "이미지에 공개된 'Port' 정보가 없습니다.", "workload_containerImage_error_message_access_token": "이미지 조회 권한이 없습니다. 프로필에서 인증정보를 확인 하세요.", "workload_containerImage_error_message_not_found_tag": "이미지 'Tag' 정보를 조회 할 수 없습니다. 저장소 유형, 이미지 주소, 프로필에서 인증정보를 확인 하세요.", "workload_containerImage_error_message_not_found_exposed_ports": "공개된 'Port' 정보를 조회 할 수 없습니다. 저장소 유형, 이미지 주소, 프로필에서 인증정보를 확인 하세요.", "workload_containerImage_error_message_manifests": "컨테이너 이미지의 Manifests 정보를 조회 할 수 없습니다.", "workload_containerImage_error_error_message": "플랫폼에서 알수 없는 오류가 발생 하였습니다. 관리자에게 문의 하세요.", "workload_containerImage_error_message_not_found_exposed_ports2": "공개된 'Port' 정보를 조회 할 수 없습니다.", "workload_containerImage_error_message_check_credential_address": "프로필에서 인증정보 및 컨테이너 이미지 주소를 확인하세요.", "workload_modify_gpu_warning_message": "선택하신 GPU 사양의 노드가 없을 경우,<br />워크로드 배포에 문제가 발생할 수 있습니다.<br />이 내용으로 변경하시겠습니까?", "workload_noselected_gpu_warning_message": "선택하신 GPU가 없습니다.<br />GPU를 선택해 주세요.", "workload_credential_msg": "도커허브 저장소 사용 시 <br />도커허브 계정 인증이 필요합니다.<br />개인인증정보를 등록 후 사용해주세요.<br/><br/>도커허브 계정이 없는 경우 신규 계정 개설 후<br/>등록하거나 다른 저장소를 선택해주세요.", "workload_credential_msg2": "도커허브 저장소 사용 시<br />도커허브 계정 인증이 필요합니다.<br />개인인증정보를 등록 후 사용해주세요.<br/><br/>도커허브 계정이 없는 경우 신규 계정 개설 후 <br/>등록하거나 다른 저장소를 선택해주세요.<br/>(인증정보 미등록 시 워크로드 종료)", "workload_pv_message01": "* 개인 저장소 사용을 위해서는 개인 저장소 등록 후 선택할 수 있습니다.", "workload_pv_message02": "* 저장소 선택 후 data 를 저장할 컨테이너 디렉토리를 입력해주세요.", "workload_pv_link": "등록 바로가기", "pod_label_ssh": "컨테이너 SSH 접속", "pod_label_publicIp": "공인 IP", "pod_label_ssh_url": "SSH 접속 주소", "pod_label_ssh_port": "SSH 접속 포트", "pod_label_ssh_publicIp": "공인 IP 주소", "pod_label_ssh_user_name": "사용자 아이디", "pod_label_ssh_user_pwd": "사용자 비밀번호", "pod_ssh_description": "컨테이너 SSH 접속은 공인IP를 먼저 등록하셔야 합니다.", "pod_ssh_info_description": "공인 IP 주소가 변경되면 접속정보 삭제 후 다시 공인 IP를 등록해 주세요.", "pod_publicIp_required": "공인IP를 확인하세요.", "pod_ssh_delete_description": "SSH 접속 정보를 삭제하겠습니까?", "pod_ssh_register_msg_failed": "SSH 접속 정보 등록 실패", "pod_ssh_delete_msg_failed": "SSH 접속 정보 삭제 실패", "pod_status_pending": "대기", "pod_status_running": "실행", "pod_status_succeeded": "성공", "pod_status_failed": "실패", "pod_status_terminating": "종료", "pod_status_unknows": "Unknows", "pod_event_res_type_added": "등록", "pod_event_res_type_modified": "수정", "pod_event_res_type_deleted": "삭제", "pod_event_type_warning": "경고", "pod_event_type_normal": "일반", "pod_event_kind_update": "업데이트", "dashboard_cluster_status": "클러스터 상태", "dashboard_node_status": "노드 상태", "dashboard_workload_status": "워크로드 상태", "dashboard_pod_status": "파드 상태", "dashboard_cluster_resource": "클러스터 리소스", "user_label_email": "이메일", "user_label_name": "성명", "user_label_cellphone": "핸드폰", "user_label_namespace": "네임스페이스", "user_label_company": "기업명", "user_label_position": "직무/역할", "user_label_createdat": "가입일시", "user_label_lastseen": "최근 로그인 일시", "user_label_point_charge": "충전 포인트", "user_label_point_amount": "결제 금액", "user_label_point_payment_req_info": "신청정보", "user_label_point_payment_customer_name": "구매자명", "user_label_point_payment_approval_number": "승인번호", "user_label_point_payment_method": "결제수단", "user_label_point_payment_datetime": "결제일시", "user_label_point_payment_type": "거래유형", "user_label_point_payment_goods": "충전결제", "user_label_point_payment_info_detail": "세부 결제 정보", "user_label_point_payment_order_krw": "적용 환율", "user_label_point_payment_order_amount": "주문금액", "user_label_point_payment_order_vat": "부가세액", "user_label_point_payment_order_point": "충전 포인트", "user_label_point_payment_amount": "결제금액", "user_label_point_payment_vat": "부가세포함", "user_label_point_payment_approval_msg": "포인트 충전 내역을 최종 확인 후<br/>아래 결제완료 버튼을 눌러주세요.", "user_label_point_payment_error_code": "장애 코드", "user_label_point_payment_error_msg": "장애 메시지", "user_label_point_settlement_bank_account": "계좌정보", "user_label_point_settlement_bank_account_name": "이름", "user_label_point_settlement_bank_number": "계좌번호", "user_label_point_settlement_bank_name": "은행명", "user_label_point_settlement_kakao_bank": "카카오뱅크로 인증 ", "user_label_point_settlement_person": "개인", "user_label_point_settlement_business": "사업자(개인)", "user_label_point_settlement_info": "정산안내", "user_label_point_settlement_msg01": "법인 사업자는 정산이 불가 합니다.", "user_label_point_settlement_msg02": "등록된 계좌가 없습니다.<br/>아래 카카오뱅크로 인증해 주세요.", "user_label_point_settlement_msg03": "정산계좌는 카카오뱅크만 사용 가능합니다.", "user_label_point_settlement_msg04": "※최종 출금액은 달러 환율이 적용된 가격입니다.<br />※세금 공제 3.3%를 제외한 금액이 입금됩니다.<br/>※출금 신청일 기준 익월 15일<br/>※출금 예정일이 휴일일 경우 익영업일에 처리", "user_label_point_settlement_account_verification_success": "인증이 완료되었습니다.", "user_label_point_settlement_account_verification_error": "인증 오류 입니다.", "user_label_storage_person_storage_register": "개인저장소 등록", "user_label_storage_upload_file": "rclone.conf 파일을 업로드 해 주세요.", "user_label_storage_list": "저장소 목록", "user_label_storage_type": "저장소 유형", "user_label_storage_description": "저장소 별명", "user_label_storage_remote_path": "저장소 경로(네트워크 드라이버의 {0} 를 입력하십시오.)", "user_label_storage_s3_directory": "버킷/디렉토리", "user_label_storage_directory": "디렉토리", "user_label_storage_access_mode": "저장소 접근방식(기본 ReadWriteMany)", "user_label_storage_capacity": " 저장소 용량 (네트워크 저장소 용량을 입력하십시오. 실제 용량보다 큰 값을 입력 하세오. 기본 10Gi)", "user_label_storage_table_th_type": "유형", "user_label_storage_table_th_desc": "별명", "user_label_storage_table_th_pv_status": "PV(Persistent Volume)", "user_label_storage_table_th_pvc_status": "PVC(Persistent Volume Claim)", "user_label_storage_table_th_token_exp_date": "토큰만료일", "user_label_storage_table_th_created_at": "등록일시", "user_label_storage_table_th_last_seen": "최근수정일시", "user_label_storage_table_th_remote_path": "경로", "user_label_storage_table_th_capacity": "용량", "user_label_storage_table_header_message01": "* 워크로드 사용 시 data 저장을 위한 개인 저장소를 연동하여 사용할 수 있습니다.", "user_label_storage_table_header_message02": "* 개인 저장소 등록 후 워크로드 생성 시 개인저장소를 선택하여 설정을 진행해주세요.", "user_label_storage_table_docs_link": "사용 가이드보기", "user_label_pv_pvc_create": "PV & PVC 생성", "user_label_pv_pvc_update": "PV & PVC 수정", "user_label_but_edit_credentials": "인증정보 수정", "user_label_storage_delete": "사용자 저장소 삭제", "user_label_storage_delete_failed": "저장소 삭제에 실패 하였습니다.", "user_label_storage_delete_message": "해당 사용자 저장소를 삭제 하시겠습니까?", "user_label_storage_result_message01": "권한 없는 요청 입니다.", "user_label_storage_result_message02": "권한 없는 요청 입니다.", "user_label_storage_result_message03": "잘못된 요청 입니다.", "user_label_storage_result_message04": "Secret 갱신 오류.", "user_label_storage_result_message05": "PVC 갱신 오류.", "user_label_storage_result_message06": "PV 갱신 오류.", "user_label_storage_result_message07": "플랫폼 오류 입니다.", "user_profile_user_info": "사용자 정보", "user_profile_edit_title": "프로필 정보 수정", "user_profile_edit_msg_failed": "프로필 수정 실패", "user_profile_user_credentials": "사용자 인증정보", "user_credential_no_content": "등록된 사용자 인증 정보가 없습니다.", "user_credential_register_title": "사용자 인증 정보 등록", "user_credential_register_msg_access_key_required": "접근키(Access Key / User Name)를 입력하세요.", "user_credential_register_msg_secret_key_required": "비밀키(Secret Key / User Password / Access Token)를 입력하세요.", "user_credential_register_msg_region_required": "지역을 입력하세요.", "user_credential_register_msg_registryid_required": "레지스트리 ID를 입력하세요.", "user_credential_register_msg_failed": "사용자 인증정보 등록 실패.", "user_credential_modify_title": "사용자 인증정보 수정", "user_credential_modify_msg_failed": "사용자 인증정보 수정 실패.", "user_credential_delete_title": "사용자 인증정보 삭제", "user_credential_delete_msg_failed": "사용자 인증정보 삭제 실패.", "user_credential_delete_msg_confirm": "해당 사용자 인증정보를 삭제 하시겠습니까?", "user_credential_label_category": "유형", "user_credential_label_cloud": "클라우드", "user_credential_label_repository": "저장소", "user_credential_label_access_key": "접근키(Access Key)", "user_credential_label_secret_key": "비밀키(Secret Key)", "user_credential_label_access_key_id": "접근키(Access Key ID)", "user_credential_label_secret_key_id": "비밀키(Secret Access Key)", "user_credential_label_user_name": "사용자 이름", "user_credential_label_user_password": "사용자 비밀번호", "user_credential_label_user_token": "사용자 토큰", "user_credential_label_user_passwordtoken": "사용자 비밀번호 또는 토큰", "user_credential_label_user_password_pat": "개인접속토큰(PAT) 또는 사용자 비밀번호", "user_credential_label_region": "지역(Region)", "user_credential_label_registryid": "레지스트리 ID(Regstry ID / Account ID)", "user_credential_table_th_category": "유형", "user_credential_table_th_type": "저장소", "user_credential_table_th_access_key": "접근 키/사용자 이름", "user_credential_table_th_secret_key": "비밀 키/사용자 비밀번호", "user_point": "포인트", "user_point_title": "사용자 포인트", "user_point_status": "포인트 상태", "user_point_network": "네트워크 상태", "user_point_available": "사용가능 포인트", "user_point_income_total": "전체 수입 포인트", "user_point_spend_total": "전체 지출 포인트", "user_point_settlement_total": "전체 정산 포인트", "user_point_payment_total": "결제 포인트", "user_point_income_month": "이번달 수입 포인트", "user_point_spend_month": "이번달 지출 포인트", "user_point_month": "이번달 포인트", "user_point_history_title": "포인트 내역", "user_point_income": "수입 포인트", "user_point_spend": "지출 포인트", "user_point_charge_title": "포인트 충전", "user_point_settlement_history": "포인트 정산 내역", "user_point_income_status": "수입 포인트 현황", "user_point_withdrawal_points": "출금 가능 포인트", "user_point_payments": "포인트 결제 내역", "user_point_payment_order_id": "주문번호", "user_point_payment_error_code": "오류코드", "user_point_payment_error_msg": "오류메세지", "user_point_payment_charge_point": "충전포인트", "user_point_payment_amount": "결제금액", "user_point_payment_key": "결제키", "user_point_payment_method": "결제 방법", "user_point_payment_charge_title": "충전 포인트 결제", "user_point_payments_dedicated": "결제 내역", "user_point_payment_msg_failed": "포인트 충전이 실패하였습니다.", "user_point_payment_msg_success": "포인트 충전이 완료되었습니다.", "user_point_payment_msg_cancel": "포인트 결제 취소", "user_point_payment_msg_point_required": "충전 포인트를 입력하세요.", "user_point_payment_msg_point_range": "충전 포인트는 20,000 ~ 1,000,000,000 까지 충전 할 수 있습니다.", "user_point_payment_msg_method_required": "결제 방법을 선택 하세요.", "user_point_payment_msg_ready_failed": "결제 요청 실패", "user_point_payment_msg_approval_failed": "결제 승인 요청 실패", "user_point_payment_msg_invalid": "인증되지 않은 접근 입니다.", "user_point_payment_msg_amount": "※최종 결제 금액은 VAT 금액이 적용된 가격입니다.", "user_point_payment_msg_comment": "포인트 현황에서 결제하신 내역을 확인하실 수 있습니다.", "user_point_payment_method_card": "신용 · 체크카드", "user_point_payment_method_acct": "계좌이체", "user_point_payment_method_vacct": "가상계좌", "user_point_payment_status_progress": "결제진행", "user_point_payment_status_done": "정상처리", "user_point_payment_status_failed": "결제실패", "user_point_payment_status_canceled": "결제취소", "user_point_payment_status_expired": "결제유효시간만료", "user_point_payment_status_partial_canceled": "부분취소", "user_point_settlement_title": "포인트 정산", "user_point_settlement_all": "전체 포인트", "user_point_settlement_withdraw_available": "정산 가능 포인트", "user_point_settlement_withdraw": "정산 포인트", "user_point_settlement_amount": "정산금액", "user_point_withdraw": "출금 포인트", "user_point_tax_deduction": "세금공제", "user_point_amount": "금액", "user_point_settlement_req_point": "정산 요청 포인트", "user_point_settlement_withdrawal_amount": "출금 금액", "user_point_settlement_msg_point_range": "정산 포인트 가능한 포인트는 1 ~ {0} 까지 입니다.", "user_point_settlement_msg_point_zero": "정산 가능한 포인트가 없습니다.", "user_point_settlement_msg_request": "정산요청을 실행 하시겠습니까?", "user_point_settlement_msg_request_failed": "정산 요청 실패", "user_point_refund_request": "포인트 환급 신청", "user_point_refundable": "환급 가능 포인트", "user_point_refund_req_point": "환급 요청 포인트", "user_point_refund_point": "환급 포인트", "user_point_refund_fee": "환급 수수료", "user_point_refund_withdrawal_amount": "출금 금액", "user_point_refund_msg_point_zero": "환급 가능한 포인트가 없습니다.", "user_point_refund_msg_point_range": "환급 가능한 포인트느 1 ~ {0} 까지 입니다.", "user_point_refund_msg_request_failed": "환급 신청 실패.", "user_point_refund_msg_execute_request": "환급 요청을 실행 하시겠습니까?.", "user_point_refund_msg_bank_account": "은행 계좌 정보를 확인 하세요.", "user_notifications_title": "알림", "user_notifications_view_details": "상세내용 보기", "user_notification_history_info": "알림 내역은 최근 90일까지 보여지며 이전 내용은 삭제됩니다.", "user_notifications_view_list": "목록", "user_notification_detail_title": "[gcube] 서비스 이용약관 및 개인정보 처리방침 변경 안내", "user_notification_detail_intro": "안녕하세요, gcube 입니다.\n\ngcube의 서비스 이용약관 일부가 아래와 같이 개정될 예정임을\n안내드립니다.", "user_notification_detail_changes_title": "1. 주요 개정내용", "user_notification_detail_change_item1": "포인트 기준 단위 변경 (1,000P = 1달러 ➔ 1P = 1원)", "user_notification_detail_change_item2": "유/무상 포인트 및 수익 포인트의 구분", "user_notification_detail_change_item3": "무료 포인트 유효기간 제도 도입", "user_notification_detail_change_item4": "포인트 환불, 환급, 정산 기준 추가", "user_notification_detail_change_item5": "개인정보 처리 위탁 업체 변경 (SPC섹타나인 ➔ 토스페이먼츠)", "user_notification_detail_view_details": "신·구조문대비표 자세히보기", "user_notification_detail_effective_date": "2. 시행일자 : 2025년 4월 21일", "user_notification_detail_objection": "서비스 이용약관 개정에 동의하지 않으시는 경우 회원탈퇴나 고객센터를 통해 이의 제기가 가능합니다. 공지기간 동안 별도의 이의 제기가 없으시면 약관 개정에 동의한 것으로 보아 시행일로부터 개정된 약관이 적용됩니다.", "user_notification_detail_footer": "더 나은 서비스를 제공하기 위해 최선을 다하겠습니다.\n\n감사합니다.", "user_notification_2_detail_title": "[gcube] 포인트 단위 변경 안내", "user_notification_2_detail_intro": "안녕하세요 , gcube 입니다 .\n공지된 바와 같이 gcube 포인트의 전환 기준이 변경되어 회원님이\n보유하고 있는 포인트가 변경되어 짐을 안내드립니다 .\n충전 및 사용 시 포인트 전환 기준 통화가 달러에서 원화로 변경되며\n포인트가 변경되어도 사용가치는 변함이 없습니다 .\n 이는 기존 이용 시 환율을 적용한 포인트로 계산해서 확인하는\n 불편함을 해소하기 위한 개편으로 보다 편리하게 사용할 수 있도록 \n변경하오니 많은 이용부탁드리겠습니다 .", "user_notification_2_detail_1_changes_title": "포인트 변경 예시", "user_notification_2_detail_1_change_item1": "현재 보유 포인트 : P 1,000", "user_notification_2_detail_1_change_item2": "현재 보유 포인트의 원화 금액 : 1,435 원", "user_notification_2_detail_1_change_item3": "변경 보유 포인트 : P 1,435", "user_notification_2_detail_1_change_item4": "변경 보유 포인트의 원화 금액 : 1,435 원", "user_notification_2_detail_2_changes_title": "포인트 변경 일시", "user_notification_2_detail_2_change_item1": "2025 년 4 월 21 일", "user_notification_2_detail_2_change_item2": "* 기준 환율 1435 원 일 경우 변경 예시", "user_notification_2_detail_2_change_item3": "* 무료로 지급된 포인트의 경우 무료 포인트에 대해서는 환율 적용 없이 지급된 포인트 그대로 유지되어집니다 .", "user_notification_2_detail_objection": "유상 포인트 보유 회원님의 자세한 포인트 변경 내용은 이메일로 발송\n예정입니다 . 변경된 포인트에 문의가 있는 경우 홈페이지의 카카오톡\n문의나 이메일을 통해 언제든 문의주시기 바랍니다 .", "user_notification_2_detail_footer": "gcube 는 언제나 더 나은 서비스를 제공하기 위해 최선을 다하겠습니다 .\n\n감사합니다.", "user_notifications_delete_error_msg": "알림을 삭제 할 수 없습니다.", "user_storage_msg_description": "저장소 별명을 입력하세요.", "user_storage_msg_remote_path": "저장소 디렉토리 경로를 입력하세요.", "user_storage_msg_access_mode": "저장소 접근 방식을 선택하세요.", "user_storage_msg_capacity": "저장소 용량을 입력하세요.", "user_storage_msg_access_key_id": "Access Key Id를 입력하세요.", "user_storage_msg_secret_access_key": "Secret Access Key를 입력 하세요.", "user_storage_msg_region": "Region을 입력 하세요.", "pricing_title": "클라우드 GPU 가격 비교", "pricing_msg": "다음은 클라우드 공급업체의 다양한 GPU 모델과 가격 범위에 대한 개요입니다.", "pricing_updated": "갱신일자", "pricing_onrequest": "요청시", "pricing_hr": "시간", "session_expire_title": "알림", "session_expire_description": "인증이 만료되었습니다.", "session_extends_description1": "세션 종료 <span>{0}</span> 전 입니다.", "session_extends_description2": "세션 시간을 연장하시겠습니까?", "session_extends_description3": "개인별 세션시간은 개인설정에서 변경하실 수 있습니다.", "auth_terms": "이용약관", "auth_privacy": "개인정보처리방침", "auth_marketing": "마케팅 정보 수신 동의", "auth_signin": "로그인", "auth_signup": "회원가입", "auth_contactus": "문의하기", "signin_title": "GCUBE 로그인", "signin_desc": "GCUBE에 로그인하여 노드 및 워크로드를 생성해 보세요.", "signin_but_google": "구글 로그인", "signin_but_microsoft": "마이크로소프트 로그인", "signin_but_naver": "네이버 로그인", "signin_msg_failed": "로그인 실패", "signin_msg_already": "이미 가입된 E-Mail 입니다.", "signup_title": "GCUBE 회원가입", "signup_terms_title": "약관동의", "signup_input_title": "회원정보입력", "signup_desc": "GCUBE 가입하여 노드 및 워크로드를 생성해 보세요.", "signup_but_google": "구글 회원가입", "signup_but_microsoft": "마이크로소프트 회원가입", "signup_but_naver": "네이버 회원가입", "signup_terms_required": "필수", "signup_terms_optional": "선택", "signup_terms_viewall": "전체", "signup_terms_agreeall": "전체 동의 하기", "signup_terms_agreeall_desc": "gcube 이용약관(필수), 개인정보 수집 및 이용(필수) 동의를 포함합니다.", "signup_label_email": "이메일", "signup_label_name": "회원이름", "signup_label_phone": "핸드폰 번호", "signup_label_phone_desc": "핸드폰번호: 00000000000", "signup_label_company": "기업명", "signup_label_industry_desc": "작성예시: 소프트웨어 개발 및 공급업/개발자", "signup_label_position": "직무/역할", "signup_label_position_desc": "작성예시: 데이터얼라이언스 운영기획본부/책임 연구원", "signup_msg_name_required": "이름을 입력하세요.", "signup_msg_phone_required": "핸드폰을 입력하세요.", "signup_msg_phone_invalid": "핸드폰 형식이 올바르지 않습니다.", "signup_msg_failed": "사용자 회원가입 실패", "signup_msg_success": "사용자 회원가입 성공", "singup_input_description01": "마지막으로 아래의 정보를 입력해 주세요. ", "singup_input_description02": "gcube 이용에 도움을 드릴 업데이트와 이벤트 소식을 전해 드립니다.", "unauthorized_msg01": "로그인 세션이 만료되었습니다.", "unauthorized_msg02": "아래 버튼을 클릭하면 로그인 페이지로 이동합니다.<br />불편을 끼쳐드려 죄송합니다.", "unauthorized_agent_msg01": "에이전트 인증에 실패 하였습니다.", "unauthorized_agent_msg02": "아래 버튼을 클릭하면 로그인 페이지로 이동합니다.<br>불편을 끼쳐드려 죄송합니다.", "forbidden_msg01": "리소스에 접근할 수 없습니다.", "forbidden_msg02": "이 리소스에 접근할려면 인증이 필요 합니다.", "landing_menu_pricing": "가격표", "landing_menu_docs": "문서", "landing_menu_faq": "고객지원", "landing_menu_blog": "블로그", "landing_footer_da": "데이터얼라이언스 주식회사", "landing_footer_ceo": "gcube 대표이사 이광범", "landing_footer_brn": "사업자 등록번호 415-81-56947", "landing_footer_mon": "통신판매번호 2022-서울강남-0448", "landing_footer_address": "서울특별시 강남구 강남대로 484, 410호(논현동, 패스트파이브)", "index_msg01": "최대 70% 경제적인 <br class=\"text-[56px]\" /> GPU Cloud 플랫폼", "index_msg02": "GPUaaS의 새로운 선택!", "index_msg03": "GPU 자원을 함께 나누어 더 많은 가치를 창출 합니다.", "index_msg031": "gcube는 전 세계 유휴 GPU 자원을 공유하여, GPU 수요자에게 최대 70% 경제적인 클라우드 컴퓨팅 서비스를 제공하고, GPU 보유자에게는 추가 수익 창출 기회를 제공합니다.<br/> 이를 통해 공급자와 수요자 간의 상호 이익을 극대화하는 플랫폼입니다.", "index_msg032": "AI서비스를 위한<br/>베타테스터 신청", "index_msg04": "효율적이고 경쟁력 있는 서비스", "index_msg05": "새로운 세상을 먼저 경험해 볼 수 있는 베타 테스트에 초대 합니다!", "index_msg06": "※1달 기준 최대 예상 수익<br class=\"py-3\">※공급량에 따라 수익은 달라질 수 있습니다.", "index_msg07": "GPU를 공유하고 수익을 창출하세요.", "index_msg08": "보유중인 유휴 GPU를 gcube에 연결하여 월 최대 178달러의 안정적인 부가수익을 창출하세요.", "index_msg09": "GPU공유 방법 보기", "index_msg10": "※해당 금액은 최소 금액기준입니다.", "index_msg11": "AI서비스 운영비용을 절약하세요.", "index_msg12": "필요한 시간과 규모만큼 컴퓨팅하고,<br /> 비용은 월 최대 70% 아끼세요.", "index_msg13": "가격표 자세히 보기", "index_msg14": "GPU 자원의 혁신적 활용으로 <br/>클라우드 컴퓨팅 서비스를 바꿉니다.", "index_msg15": "gcube는 Global의 GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU공유경제 서비스입니다.", "index_msg16": "※데이터 센터용 GPU(T4, V100, A100)는 타사대비 최대 50% 비용이 저렴합니다.<br class=\"py-3\">※기본료를 포함한 총 비용은 100%를 넘지 않습니다.", "index_msg17": "클라우드 컴퓨팅 이용에 따른 과금 정책", "index_msg18": "이용한 만큼의 과금방식으로 사용률이 낮을때는 타사대비 최대 70% 비용을 절감할 수 있으며, 최대 이용률일경우에도 타사보다 경쟁력 있는 가격으로 이용하실 수 있습니다.", "index_msg19": "GPU 클라우드 서비스의 문제는 비용입니다.", "index_msg20": "클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스등 서비스의 특성에 맞게 맞춤형으로 안정적인 서비스를 제공할수 있습니다.", "index_msg21": "Gcube를 통해 전세계의 유휴 GPU를 네트워킹을 한다면, AI서비스 제공자에게는 저렴하게 확장성이 뛰어난 고성능 클라우드 컴퓨팅 서비스를 제공할 수 있고, GPU 리소스 공급자는 부가적인 수익을 얻을 수 있는 기회를 가질 수 있습니다.", "index_msg22": "대규모 데이터 처리", "index_msg23": "기술적 우위", "index_msg24": "GPU의 강력한 연산 능력을 활용", "index_msg25": "GPU의 강력한 연산 능력을 활용하여 머신 러닝 모델 훈련, <br />딥 러닝, 데이터 분석 등의 작업을 빠르게 처리할 수 있습니다.", "index_msg26": "애플리케이션을 손쉽게 배포 및 관리", "index_msg27": "작업 부하에 유연하게 대응할 수 있으며 애플리케이션을 손쉽게 배포하고 관리할 수 있습니다.", "index_msg28": "서버리스 환경", "index_msg29": "서버리스 환경에서 다양한 클라우드 플랫폼이나 서비스를 동일하게 실행할 수 있습니다.", "index_msg30": "GPU공급자를 Tier1별 분류하여<br /> AI서비스 회사의 QoS 요구사항에 맞추어 최적의 서비스 제공", "index_msg31": "최대 70% 경제적인 서비스", "index_msg32": "비용 최적화 솔루션", "index_msg33": "서비스 레벨 계약 (SLA)에 따라 최적의 비용을 산출하여 자동으로 서비스를 확장해 나갈 수 있습니다.", "index_msg34": "편리한 운영 효율성", "index_msg35": "기존 서비스와 100% 호환되는 컨테이너", "index_msg36": "공유자가 공유한 분산 네트워크에 있는 GPU와 주요 클라우드 서비스를 동시에 이용할 수 있도록 해주는 완전 관리형 컨테이너 서비스를 제공합니다.", "index_msg37": "국제특허 받는 투명성", "index_msg38": "공유 기여도에 따른 투명한 보상", "index_msg39": "블록체인기술을 활용하여 컴퓨팅 자원 사용률을 투명하게 관리하며, 국제특허 기반으로 공유 참여자에게 기여도에 따른 보상을 제공합니다.", "index_msg40": "알파테스터의 목소리", "index_msg41": "서비스를 준비하는 과정에서 테스터로서<br />도움을 주신 분들의 생각과 의견을 공유합니다.", "index_msg42": "블로그", "index_msg43": "최신 소식이나 업계 트렌드를 확인해보세요.", "index_msg44": "gcube의 협력 파트너", "index_msg45": "gcube는 네이버클라우드의 투자와 사업협력으로<br/> 글로벌 파트너 네트워크를 넓혀가고 있습니다.", "index_msg46": "생산성 및 효율성 향상", "index_msg47": "지금 바로 문의하세요.", "index_msg48": "gcube가 어떻게 도울 수 있는지 알아보고 싶으신가요?", "index_msg49_01": "클라우드 사업자", "index_msg49_02": "네이버클라우드, A<PERSON>, Azure,<br /> NHN 클라우드, KT 클라우드", "index_msg49_03": "24시간 끊김없이 GPU 리소스를 공급 및 사용", "index_msg50_01": "전용 서버", "index_msg50_02": "클라우드사업자, 데이터센터, PC방", "index_msg50_03": "-계약을 통한 GPU 서버 제공 <br />(gcube 서버 형태)", "index_msg50_04": "24시간 끊김없이 GPU 리소스를 공급 및 사용", "index_msg51_01": "Interruptible 방식", "index_msg51_02": "PC방,개인", "index_msg51_03": "-공급자 맞춤형 GPU 공유를 통한 <br />비용 절감에 최적화된 GPU 리소스<br />", "index_msg51_04": "원하는 시간대에 GPU 리소스를 공급 및 사용", "index_msg40_01": "사운드 마인드", "index_msg40_02": "AI 서비스를 제공하는 기업에서 가장 중요하게 고려하는 것은 바로 AI 학습과 추론 과정에서 발생하는 컴퓨팅 비용입니다. 평소 학습과 추론에서 매달 발생하는 클라우드 비용이 부담이 되었습니다. 현재 저희 사운드 마인드에서는 gcube 서비스의 첫 테스터 기업으로서 gcube로 전환 중입니다. 자체 보유한 장비도 있으나, 해당 장비의 유휴 시간에는 GPU를 공유하여 추가적인 수익을 얻을 수 있었고, gcube를 통해 컴퓨팅 운영비용이 55% 절감되는 효과를 경험했습니다.", "index_msg40_03": "PC방", "index_msg40_04": "저희 1% PC방은 테스트를 위해 고정적으로 PC 8대를 24시간 가동하고 있습니다. 손님이 없는 유휴 시간대에 PC가 돌아가는 모습을 보면서 마음 한켠이 가벼워졌습니다. 저는 gcube를 통해 최소한 전기세나 야간 인건비라도 운영비용이 어느 정도 상쇄된다면 충분히 시도해볼 만하다고 생각합니다. 전국의 많은 PC방들이 앞으로는 gcube에서 GPU를 공유하고 추가 수익을 내는 방향으로 전환될 것 같다는 생각도 듭니다. 저희 1% PC카페는 조만간 모든 PC에서 진행할 계획입니다. 제가 PC방을 대표하는 건 아니지만, 별도의 장비 투자 없이 추가적인 수익을 낼 수 있다는 것은 PC방 입장에서 굉장히 좋은 기회라고 생각합니다.", "index_msg40_05": "백엔드 개발자", "index_msg40_06": "gcube 서비스의 알파 테스터로 참여한 koo입니다. gcube는 기존의 클라우드 서비스와는 다르게 사용기반에 따른 비용이 다르게 부과되는 점이 인상적이었습니다. 개발자 입장에서 비용은 매우 민감한 부분이기 때문에 더 크게 다가왔던 것 같네요. 물론, 테스트하는 과정에서 버그도 있고, 때로는 예측하기 어려운 결과를 내놓기도 했지만 gcube팀의 열정적인 대응과 지속적인 개선 노력을 직접 목격하면서, gcube 서비스의 큰 기대를 갖게 되었습니다. gcube 서비스가 앞으로 어떻게 발전하고 어떻게 변화될지 정말 기대됩니다.", "gpu_top_msg00": "유휴 GPU로 새로운 수익 창출의 즐거움을 함께 하세요", "gpu_top_msg01": "gcube 에이전트를 설치하여 언제든 유휴 GPU를 공유하고 수익을 창출 할 수 있습니다.", "gpu_body_msg01": "매우 간단합니다. <br/> Agent를 다운로드 하고", "gpu_body_msg02": "Agent를 실행합니다.", "gpu_body_msg03": "GPU 공유를 상태를 확인하고<br />수익을 확인합니다.", "gpu_body_msg04": "GPU의 공유는 언제든 가능하지만, 원활한 서비스를 제공하기<br /> 위해서는 필요한 하드웨어 성능이 존재 합니다.", "gpu_body_msg05": "최고의 성능과 수익을 위해 아래의 사양을 확인해 주세요.", "gpu_body_msg06": "설치 PC 기본 사양", "gpu_body_msg07": "권장 사양", "pricing_msg01": "GPUs 지출을 줄이고 싶나요?<br />당장 시작하세요~", "pricing_msg02": "최대 70% 경제적인 기본 요금에 사용한 만큼만 지불하세요.<br />GPU 공유 클라우드의 가격 혁신은 지큐브에서 시작됩니다.", "pricing_msg03": "GPU, vRAM, vCPU, STORAGE, RAM에 원하는 사양에 맞게 선택할 수 있으며 <br />최대 이용율일 경우에도 타사보다 경쟁력 있는 가격으로 이용하실수 있습니다.", "pricing_msg04": "통화", "pricing_msg05": "※요금은 시간에 따라 변동될 수 있습니다.", "pricing_msg06": "네트워크 요금", "pricing_msg07": "아웃바운드 트래픽에 대한 네트워크 사용량은 다음 요금표에 따라 과금합니다.", "pricing_msg08": "※환율의 적용은 공신력 있는 기관의 전월 마지막 최종 고시환율을 기준으로 하며, 이용 당월 1일부터 적용합니다.", "pricing_msg09": "※환율은 매월 변동되며, 매월 1일~말일 까지는 동일한 환율을 적용합니다.", "pricing_msg10": "지금 바로 문의하세요.", "pricing_msg11": "도움이 필요하신가요? 지원팀에 문의하여 문의 사항 및 우려 사항에 대해 신속하고 맞춤화된 도움을 받으세요.", "pricing_msg12": "클라우드 사업자", "pricing_msg13": "전용 서버", "pricing_msg14": "개인, PC방", "pricing_table_th01": "최저 가격 /hr", "pricing_table_th02": "사용 기반에 따른", "pricing_table_th03": "구분", "pricing_table_th04": "사용량 구분", "pricing_table_th05": "요금(GB 당)", "pricing_table_th06": "최대 가격 /hr", "pricing_table_td01": "인바운드 트래픽", "pricing_table_td02": "전 구간 동일", "pricing_table_td03": "아웃바운드 트래픽<br/>(인터넷을 이용한 트래픽)", "pricing_table_td04": "인/아웃바운드 트래픽", "pricing_table_td_le": "{0} 이하", "pricing_table_td_range": "{0} 초과 ~ {1} 이하", "pricing_table_td_gt": "{0} 초과", "pricing_table_min": "최소", "faq_msg01": "아래에서 gcube에 자주 묻는 질문(FAQ)를 확인해 보세요.", "faq_msg02": "자주 묻는 질문", "contactus_msg01": "gcube는 여러분과 연결되기를 <br/>무척 기대하고 있습니다.", "contactus_msg02": "우리의 서비스에 궁금한 모든 이야기를 언제든 이야기 해주세요. <br/>전화나 메일을 통해, gcube에 대해 자세히 알려 드립니다.", "contactus_msg03": "gcube를 사용하여 어떤 AI 모델을 만들 수 있나요?", "contactus_msg04": "gcube 결제 방식은 어떻게 해야 하나요", "contactus_msg05": "gcube를 통해 수입을 올리려면 어떻게 해야 하나요?", "contactus_label_familyname": "성", "contactus_label_name": "이름", "contactus_label_email": "업무용 이메일", "contactus_label_country": "국가/지역", "contactus_label_company": "기업명", "contactus_label_phone": "전화번호", "contactus_label_help": "무엇을 도와드릴까요?", "contactus_register_msg_familyname_required": "성을 입력하세요.", "contactus_register_msg_name_required": "이름을 입력하세요.", "contactus_register_msg_email_required": "업무용 이메일을 입력하세요.", "contactus_register_msg_country_required": "국가를 선택하세요.", "contactus_register_msg_company_required": "기업명을 입력하세요.", "contactus_register_msg_phone_required": "전호번호를 입력하세요.", "contactus_register_msg_inquiry_required": "문의사항을 입력하세요.", "contactus_register_msg_email_invalid": "이메일 형식이 아닙니다.", "contactus_register_msg_phone_invalid": "전화번호 형식이 아닙니다.", "contactus_register_msg_send_email": "문의 메일을 발송 하였습니다.", "qna_msg_title01": "* FAQ에서 더 빠른 답변을 확인하실 수 있습니다.", "qna_msg_title02": "* 답변을 확인할 수 없다면 문의하기를 이용해 직접 문의해주세요.", "qna_register_msg_select": "선택", "qna_register_msg_type": "문의 유형", "qna_register_msg_name": "* 이름", "qna_register_msg_title": "* 제목", "qna_register_msg_content": "* 내용", "qna_register_msg_target": "문의대상", "qna_register_msg_target_workload": "워크로드", "qna_register_msg_target_node": "노드", "qna_register_msg_send_qna": "문의하기", "qna_register_msg_kakao_chat": "카카오 채팅", "qna_register_msg_success": "문의가 접수 되었습니다.", "qna_register_msg_failed": "문의 접수에 실패 하였습니다.", "qna_register_msg_type01": "계정", "qna_register_msg_type02": "개인 인증", "qna_register_msg_type03": "노드", "qna_register_msg_type04": "워크로드", "gate_node_msg01": "<span style=\"color: #8247ff\">GPU를 공유</span>하고<br/>수익을 창출하세요.", "gate_node_msg02": "보유중인 유휴 GPU를 gcube에 연결하여<br>월 최대 178달러의 안정적인 부가수익을 창출하세요.", "gate_workload_msg01": "<span style=\"color: #8247ff\">AI서비스 운영비용</span>을<br/>절약하세요.", "gate_workload_msg02": "필요한 시간과 규모만큼 컴퓨팅하고,<br>비용은 월 최대 70% 아끼세요.", "http_bad_request": "잘못된 요청입니다.", "http_bad_request_msg": "죄송합니다.<br/>  현재 페이지는 이용할 수 없는 페이지입니다. <br/> 아래의 버튼을 눌러 페이지를 이동하세요.", "http_internal_server_error": "시스템 에러", "http_internal_server_error_msg": "죄송합니다.<br/> 현재 페이지는 요청한 정보를 처리할 수 없습니다. <br/> 아래의 버튼을 눌러 페이지를 이동하세요.", "ws_disconnected_error_msg": "WebSocket 연결이 끊어졌습니다.<br />잠시후 다시 시도해주세요.", "ws_exit_msg": "컨테이너 터미널이 종료되었습니다.", "personal_setting_title": "개인 설정", "personal_setting_session_time": "세션유지시간", "personal_setting_30min": "30분", "personal_setting_60min": "60분", "personal_setting_90min": "90분", "personal_setting_120min": "120분", "main_banner_01_msg01": "네이버클라우드 마켓플레이스 입점 기념", "main_banner_01_msg02": "AI를 위한 최저가<br/> GPU 패키지", "main_banner_01_msg03": "AI GPU 렌탈 비용의 상식을 깹니다.", "main_banner_01_msg04": "개발 속도는 올리고, 비용은 내리고", "main_banner_01_msg05": "가격 부담으로 AI의 검증이 어려웠다면<br/>이제 비용 걱정없이 마음껏 이용하고 더 나은 서비스를 만드세요.<br/>성공적인 서비스를 위해 gcube가 함께하겠습니다.", "main_banner_02_msg01": "창조경제혁신센터, 테크노파크 협약 기념", "main_banner_02_msg02": "최신 RTX5090 까지<br/> PoC 무상지원", "main_banner_02_msg03": "최신 GPU부터 원하는 스펙의 GPU를 <br/> gcube에서 무료 제공합니다.", "main_banner_02_msg04": "다양한 GPU를 통한 기술검증이 필요한 기업, AI개발은 했는데<br/> GPU 부족으로 원하는 PoC를 진행하지 못하는 개발자 등 GPU가 필요한 <br/>모든 분 지금 바로 gcube에게 PoC 무상지원 받으세요!", "main_banner_03_msg01": "Global GPU Grid", "main_banner_03_msg02": "최대 70% 경제적인<br/>GPU Cloud 플랫폼", "main_banner_03_msg03": "합리적인 GPU 사용비용으로 <br/>모두가 사용가능한 AI 인프라를 제공합니다.", "main_banner_03_msg04": "gcube는 전 세계 유휴 GPU를 공유하여, GPU 사용자에게는 <br/> 최대 70% 경제적인 클라우드 컴퓨팅 서비스를 제공하고,<br/>GPU 공급자에게는 추가 수익 창출 기회를 제공합니다."}}