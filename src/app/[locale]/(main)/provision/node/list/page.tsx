import { getNodes } from '@/action/node-action';
import { mapSearchParamsToNodeRequest, NodeRequest, Node } from '@/types/node';
import { dateTime, utcTime } from '@/utils';

import { FaPlus } from 'react-icons/fa6';

import { NodeFooter } from './node-footer';
import { NodeSpec } from './node-spec';
import { getLocale, getTranslations } from 'next-intl/server';

import Link from 'next/link';
import { getPointBase } from '@/action/point-action';
import { NodeIncome } from './node-income';
import { getSession } from '@/auth';
import NodeState from './node-state';

type PageProps = {
  searchParams: { [key: string]: string | string[] | undefined };
};

/**
 * @brie Node 목록 페이지
 * @param param0
 * @returns
 */
export default async function NodeListPage({ searchParams }: PageProps) {
  const locale = await getLocale();
  const t_i18n = await getTranslations('i18nData');

  const session = await getSession();

  //환율
  const pointBaseResponse = await getPointBase();
  const krw = pointBaseResponse.status == 200 ? pointBaseResponse.data.krw : 0;

  //url에서 파라메터 가져오기
  searchParams['startNum'] = '-1';
  searchParams['scaleNum'] = '-1';

  const urlSearchParams = new URLSearchParams(searchParams as any);

  //API 요청을 위한 query parameter로 변환
  const queryParam: NodeRequest = mapSearchParamsToNodeRequest(urlSearchParams);

  // node 목록 요청
  const data = await getNodes(queryParam);

  // node 목록 세팅
  const nodes: Node[] = data.nodes;

  /**
   * @brief Cloud 이름 변경
   * @param item
   * @returns
   */
  const convertCloudName = (item: Node) => {
    let category = '';

    if (item.category === 'pc') {
      category = 'PC';
      return (
        <>
          <span className="text-2sm font-medium text-violet-600">{`${category}`}</span>
        </>
      );
    } else if (item.category === 'svr') {
      category = 'SERVER';
      return (
        <>
          <span className="text-2sm font-medium text-violet-600">{`${category}`}</span>
        </>
      );
    } else if (item.category === 'csp') {
      category = 'CLOUD';

      if (session.user.roles.includes('ROLE_ADMIN')) {
        return (
          <>
            <span className="text-2sm font-medium text-violet-600">{`${category}`}</span>
            {item.cloud === 'aws' && <img src={`/da/img/ico_aws40.png`} style={{ height: '23px' }} alt="aws logo" />}
            {item.cloud === 'ncp' && <img src={`/da/img/ico_ncp32.png`} style={{ height: '23px' }} alt="ncp logo" />}
            {item.cloud === 'lambda' && <img src={`/da/img/ico_lambda40.png`} style={{ height: '18px' }} alt="lambda log" />}
          </>
        );
      } else {
        return (
          <>
            <span className="text-2sm font-medium text-violet-600">{`${category}`}</span>
          </>
        );
      }
    } else {
      return '';
    }
  };

  /**
   * @brief 상태 변경
   * @param item
   * @returns
   */
  const convertState = (item: string) => {
    if (item == 'open') {
      return (
        <span className="badge-gray badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[16px]">
          {t_i18n('node_status_open')}
        </span>
      );
    } else if (item == 'provis') {
      return (
        <span className="badge badge-pill badge-sm badge-outline badge-success text-nowrap !px-4 !text-[16px]">
          {t_i18n('node_status_provision')}
        </span>
      );
    } else if (item == 'run') {
      return (
        <span className="badge badge-pill badge-sm badge-outline badge-success text-nowrap !px-4 !text-[16px]">
          {t_i18n('node_status_running')}
        </span>
      );
    } else if (item == 'fail') {
      return (
        <span className="badge badge-pill badge-sm badge-outline badge-danger text-nowrap !px-4 !text-[16px]">
          {t_i18n('node_status_fail')}
        </span>
      );
    } else if (item == 'stop') {
      return (
        <span className="badge badge-pill badge-sm badge-outline badge-warning text-nowrap !px-4 !text-[16px]">
          {t_i18n('node_status_stop')}
        </span>
      );
    } else if (item == 'create') {
      return (
        <span className="badge-gray badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[16px]">
          {t_i18n('node_status_created')}
        </span>
      );
    }
  };

  if (!nodes || nodes.length == 0) {
    return (
      <>
        <div className="container-fixed pb-[60px] pt-[100px]">
          <div className="grid grid-cols-1 gap-5 lg:grid-cols-2 lg:gap-7.5">
            <div className="col-span-2">
              <div className="flex flex-col flex-wrap items-center justify-center gap-10">
                <div className="flex p-10">
                  <img src="/da/img/no_node.svg" alt=""></img>
                </div>
                <div className="flex">
                  <span className="text-[18px] font-medium leading-[30px] text-[#808C98]">{t_i18n('node_no_content')} </span>
                </div>
                <div className="flex">
                  <Link locale={locale} className="btn btn-light text-gray-700" type="button" href={`/${locale}/provision/node/register`}>
                    <FaPlus /> {t_i18n('but_register_node')}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  } else {
    return (
      <>
        <div className="container-fixed pb-[60px]">
          <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
            <div className="flex flex-col justify-center gap-2"></div>
            <div className="flex items-center gap-2.5">
              <Link locale={locale} className="btn btn-primary" type="button" href={`/${locale}/provision/node/register`}>
                <FaPlus /> {t_i18n('but_register_node')}
              </Link>
            </div>
          </div>
          <div className="grid gap-5">
            {nodes.map((item: Node, index) => (
              <div className="col-span-2" key={`node_${index}`}>
                <div className="card">
                  <div className="card-body lg:py-7.5">
                    <div className="flex grow flex-col gap-5 lg:gap-10">
                      <div className="flex flex-col gap-3 lg:gap-5">
                        <div className="flex justify-between">
                          <div className="flex flex-nowrap items-center gap-2.5 text-[14px] font-medium leading-[14px]">
                            <span className="text-gray-500">{t_i18n('node_label_category')}</span>
                            <span className="flex items-center gap-1.5 text-primary">{convertCloudName(item)}</span>
                          </div>
                          <div className="flex items-center gap-2.5">
                            <span className="text-medium text-[14px] leading-[14px] text-gray-500">
                              {t_i18n('workload_label_createdat')}
                            </span>
                            <span className="text-[14px] font-semibold leading-[14px] text-gray-900">{dateTime(item.createdAt)}</span>
                          </div>
                        </div>

                        <div className="flex flex-col justify-between gap-5">
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center justify-between gap-2.5">
                              <h2 className="text-[24px] font-semibold leading-[24px]">
                                <Link
                                  locale={locale}
                                  className="text-primary hover:link"
                                  href={`/${locale}/provision/node/info/${item.name}`}
                                >
                                  {item.name}
                                </Link>
                              </h2>
                              {/* {convertState(item.state)} */}
                              <NodeState node={item} index={index}></NodeState>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-[45px]">
                        <NodeIncome node={item} krw={krw}></NodeIncome>
                        <div className="flex flex-col gap-[30px]">
                          <NodeSpec node={item}></NodeSpec>

                          <div className="flex flex-wrap justify-end gap-3 lg:gap-5">
                            <div className="flex grow flex-col items-end gap-3.5 lg:flex-row lg:justify-end">
                              <div className="flex items-center gap-2.5">
                                <span className="text-medium text-nowrap text-[14px] leading-[14px] text-gray-500 max-lg:w-28">
                                  {t_i18n('node_label_lastseen')}{' '}
                                </span>
                                <span className="text-nowrap text-[14px] font-semibold leading-[14px] text-gray-900">
                                  {dateTime(item.lastSeen)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <span className="w-full border-t border-gray-200"></span>
                      <NodeFooter node={item}></NodeFooter>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    );
  }
}
