import { getNode } from '@/action/node-action';
import { Node } from '@/types/node';
import { MdStorage } from 'react-icons/md';

import { comma, convertCuda, dateTime, isBlank } from '@/utils';
import { IoIosArrowBack } from 'react-icons/io';
import { getLocale, getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { getPointBase } from '@/action/point-action';
import { getSession } from '@/auth';
import Page400 from '@/components/page_400';
import NodeInfoState from './node-info-state';
import NodeNetworkLineChart from './node-network-chart.tsx';

interface PageProps {
  params: {
    node: string;
  };
}
export default async function NodeInfoPage({ params }: PageProps) {
  const locale = await getLocale();
  const t_i18n = await getTranslations('i18nData');

  const session = await getSession();

  //환율
  const pointBaseResponse = await getPointBase();
  const krw: number = pointBaseResponse.status == 200 ? pointBaseResponse.data.krw : 0;

  const nodeData: any = await getNode(params.node);
  const node: Node = nodeData.node ?? null;

  if (node === undefined || node === null || node.owner !== session.user.email) {
    return <Page400 isRouter={true} routerUrl={`/${locale}/provision/node/list`}></Page400>;
  }

  // Gpu 이름 변경
  const convertGpuName = (item: string) => {
    let gpuName = '';
    if (item === 'Unknown' || isBlank(item)) {
      return '';
    } else {
      let gpus = item.split(' ');
      let vram = '';
      let count = 0;
      if (gpus[0].toLowerCase().indexOf('rtx') !== -1 || gpus[0].toLowerCase().indexOf('gtx') !== -1) {
        gpuName = 'NVIDIA GeForce ' + gpus[0].toUpperCase();
      } else {
        gpuName = gpus[0].toUpperCase();
      }
      vram = gpus[1].toUpperCase();
      if (gpus.length === 3) {
        count = parseInt(gpus[2].substring(1, gpus[2].length));
      }
      if (count == 0) {
        return gpuName + ' ' + vram;
      } else {
        return gpuName + ' x ' + count + ' ' + vram;
      }
    }
  };

  /**
   * @brief cloud 이름변경 및 아이콘 설정
   * @param item
   * @returns
   */
  const convertCategoryName = (item: Node) => {
    if (item.category === 'pc') {
      return <>PC</>;
    } else if (item.category === 'svr') {
      return <>Server</>;
    } else {
      return <>Cloud</>;
    }
  };

  /**
   * @brief Host Core 이름 변경 및 아이콘 설정
   * @param item
   * @returns
   */
  const convertHostCore = (item: string) => {
    return (
      <>
        <div className="flex gap-1">
          <div className="text-gray-500">{t_i18n('resource_cpu')}</div>
          <div>{item.split(' ').length == 3 ? item.split(' ')[0] : ''}</div>
        </div>
      </>
    );
  };

  /**
   * @brief Host Ram 이름 변경 및 아이콘 설정
   * @param item
   * @returns
   */
  const convertHostRam = (item: string) => {
    return (
      <>
        <div className="flex gap-1">
          <div className="text-gray-500">{t_i18n('resource_memory')}</div>
          <div>{item.split(' ').length == 3 ? item.split(' ')[1] : ''}</div>
        </div>
      </>
    );
  };

  /**
   * @brief Host Disk 이름 변경 및 아이콘 설정
   * @param item
   * @returns
   */
  const convertHostDisk = (item: string) => {
    MdStorage;
    return (
      <>
        <div className="flex gap-1">
          <div className="text-gray-500">{t_i18n('resource_disk')}</div>
          <div>{item.split(' ').length == 3 ? item.split(' ')[2] : ''}</div>
        </div>
      </>
    );
  };

  /**
   * @brief Cloud Logo
   * @param item
   * @returns
   */
  const cloudLogo = (item: Node) => {
    if (session.user.roles.includes('ROLE_ADMIN')) {
      if (item.category == 'csp') {
        if (item.cloud === 'aws') {
          return <img src="/da/img/ico_aws.png" style={{ height: '18px' }} alt="" />;
        } else if (item.cloud === 'ncp') {
          return <img src="/da/img/ico_ncp32.png" style={{ height: '18px' }} alt="" />;
        } else if (item.cloud === 'lambda') {
          return <img src="/da/img/ico_lambda40.png" style={{ height: '18px' }} alt="" />;
        }
      } else {
        return '';
      }
    } else {
      return '';
    }
  };
  /**
   * @brief 금액 단위 변환
   * @param item
   * @returns
   */
  const convertExchangeRate = (item: number = 0) => {
    let price = '';
    // if (locale == 'en') {
    //   price = '$ ' + (item / krw).toFixed(4);
    // } else {
    price = '₩ ' + comma(Math.round(item));
    // }

    return price;
  };

  return (
    <>
      <div className="container-fixed">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
          <div className="flex flex-nowrap items-center justify-center gap-2">
            <Link locale={locale} href={`/${locale}/provision/node/list`} className="flex flex-nowrap items-center hover:text-primary">
              <IoIosArrowBack size={'1.3em'} />
            </Link>
            <h1 className="pl-1 text-xl font-semibold leading-none text-gray-900">{t_i18n('node_info_title')}</h1>
          </div>
          {/* <div className="flex items-center gap-2.5">{node.name}</div> */}
        </div>
        <div className="grid grid-cols-1 gap-5 lg:grid-cols-1 lg:gap-7.5">
          <div className="col-span-1">
            <div className="flex flex-col gap-5 lg:gap-7.5">
              <div className="card min-w-full">
                <div className="card-body gap-8 py-5 lg:py-7.5">
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full">
                        <div className="text-lg font-semibold text-gray-900">{t_i18n('node_info_node')}</div>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_name')}</div>
                        <span className="text-2sm font-semibold text-gray-900">{node.name}</span>
                      </div>

                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_address')}</div>
                        <span className="text-2sm font-semibold text-gray-900">{node.ip}</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_createdat')}</div>
                        <span className="text-2sm font-semibold text-gray-900">{dateTime(node.createdAt)}</span>
                      </div>

                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_lastseen')}</div>
                        <span className="text-2sm font-semibold text-gray-900">
                          {node.lastSeen == null ? '--' : dateTime(node.lastSeen)}
                        </span>
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full items-center">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_state')}</div>
                        <span className="text-2sm font-semibold text-gray-900">
                          <NodeInfoState node={node}></NodeInfoState>
                        </span>
                      </div>

                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_substate')}</div>
                        <span className="text-2sm font-semibold text-gray-900">{node.subState}</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_shared')}</div>
                        <span className="text-2sm font-semibold text-gray-900">{node.share == 0 ? 'False' : 'True'}</span>
                      </div>
                      <div className="flex w-full">
                        <div className="min-w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_vpnpubkey')}</div>
                        <span className="break-all text-2sm font-semibold text-gray-900">{node.wgPubkey}</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_unitprice')}</div>
                        <span className="flex flex-nowrap items-center text-sm font-semibold text-gray-900">
                          {convertExchangeRate(node.unitPrice)} / hr
                        </span>
                      </div>{' '}
                      <div className="flex w-full">
                        <div className="w-36 text-2sm font-medium text-gray-600">{t_i18n('node_label_usageprice')}</div>
                        <span className="flex flex-nowrap items-center text-sm font-semibold text-gray-900">
                          {convertExchangeRate(node.usagePrice)} / hr
                        </span>
                      </div>
                    </div>

                    <div className="my-4 border-t border-gray-200"></div>

                    <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full">
                        <div className="flex flex-nowrap items-center text-lg font-semibold text-gray-900">
                          {t_i18n('node_info_devicespec')} - {convertCategoryName(node)}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full flex-nowrap items-center">
                        <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">{t_i18n('node_label_gpuspec')}</div>
                        <span className="flex flex-nowrap items-center text-sm font-semibold text-gray-900">
                          {convertGpuName(node.gpuSpec)}
                        </span>
                      </div>

                      <div className="flex w-full flex-nowrap items-center">
                        <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">{t_i18n('node_label_hostspec')}</div>
                        <span className="flex flex-col flex-nowrap gap-3 text-sm font-semibold text-gray-900 lg:flex-row lg:items-center">
                          <span>{convertHostCore(node.hostSpec)}</span>
                          <span>{convertHostRam(node.hostSpec)}</span>
                          <span>{convertHostDisk(node.hostSpec)}</span>
                          <span>
                            <div className="flex gap-1">
                              <div className="text-gray-500">{t_i18n('resource_network')}</div>
                              <div>{node.lastUpSpeed.toFixed(1)}MB</div>
                            </div>
                          </span>
                        </span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full flex-nowrap items-center">
                        <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">{t_i18n('node_label_driver')}</div>
                        <span className="flex flex-nowrap items-center text-sm font-semibold text-gray-900">{node.driver}</span>
                      </div>

                      <div className="flex w-full flex-nowrap items-center">
                        <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">{t_i18n('node_label_cuda')}</div>
                        <span className="flex flex-col flex-nowrap gap-3 text-sm font-semibold text-gray-900 lg:flex-row lg:items-center">
                          {convertCuda(node.cuda)}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                      <div className="flex w-full flex-nowrap items-center">
                        <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">{t_i18n('resource_network_speed')}</div>
                        <span className="flex flex-nowrap items-center text-sm font-semibold text-gray-900">
                          {node.lastUpSpeed && node.avgUpSpeed && node.lastUpSpeed.toFixed(1) + 'MB'}
                        </span>
                      </div>
                    </div>
                    <div className="my-7.5 border-t border-gray-200"></div>
                    {node.category == 'csp' && (
                      <>
                        <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                          <div className="flex w-full">
                            <div className="flex flex-nowrap items-center text-lg font-semibold text-gray-900">
                              {t_i18n('node_info_cloud')}
                              <div className="ml-2">{cloudLogo(node)}</div>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                          <div className="flex w-full">
                            <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">
                              {t_i18n('node_label_cloud_instid')}
                            </div>
                            <span className="text-sm font-semibold text-gray-900">{node.instId}</span>
                          </div>

                          <div className="flex w-full">
                            <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">
                              {t_i18n('node_label_cloud_region')}
                            </div>
                            <span className="text-sm font-semibold text-gray-900">{node.region}</span>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                          <div className="flex w-full">
                            <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">
                              {t_i18n('node_label_cloud_vmip')}
                            </div>
                            <span className="text-sm font-semibold text-gray-900">{node.vmIp}</span>
                          </div>

                          <div className="flex w-full">
                            <div className="flex w-36 flex-nowrap text-2sm font-medium text-gray-600">
                              {t_i18n('node_label_cloud_unitprice')}
                            </div>
                            <span className="flex flex-nowrap items-center text-sm font-semibold text-gray-900">
                              {convertExchangeRate(node.price)}
                            </span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                  {/*<div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">*/}
                  {/*  <div className="flex w-full pb-2">*/}
                  {/*    <div className="flex flex-nowrap items-center text-lg font-semibold text-gray-900">*/}
                  {/*      {t_i18n('resource_network_speed')}*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*</div>*/}
                  {/*<div className="flex flex-col gap-5 md:flex-nowrap lg:gap-14">*/}
                  {/*  <div className="grid1 grid">*/}
                  {/*    <div className="col-span-1 grid gap-5 p-4">*/}
                  {/*      <NodeNetworkLineChart nodeName={node.name}></NodeNetworkLineChart>*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*</div>*/}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
