'use server';

import { getHeaders } from '@/lib/fetch-header';
import { generateUrlSearchParams } from '@/utils';

// /**
//  * @brief 카테고리 코드 - 사이트 메뉴목록
//  */
// export const getCategorySiteMenus = async () => {
//   try {
//     const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/category/sitemenu`, {
//       method: 'GET',
//       headers: {
//         'Content-Type': 'application/json'
//       }
//       // cache: 'no-store'
//     });

//     if (response.ok) {
//       return await response.json();
//     } else {
//       return { status: response.status, data: null };
//     }
//   } catch (error: any) {
//     console.log(error);
//     return { status: 500, data: null, error: error.cause?.code };
//   }
// };

/**
 * @brief 카테고리 코드 목록
 */
export const getCategoryCodes = async (params: any) => {
  try {
    const queryStr = generateUrlSearchParams(params);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/category/codes?${queryStr}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      cache: 'no-store'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

/**
 * @brief 카테고리 코드 등록
 * @param data
 * @returns
 */
export const registerCategoryCode = async (data: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/category/register`, {
      method: 'POST',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(data)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

/**
 * @brief 카테고리 코드 수정
 * @param data
 * @returns
 */
export const updateCategoryCode = async (data: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/category/update`, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(data)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};
